//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Bonus.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Translations {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Translations() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Bonus.Resources.Translations", typeof(Translations).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to accept terms..
        /// </summary>
        public static string Account_AcceptTermsFailed {
            get {
                return ResourceManager.GetString("Account_AcceptTermsFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact not found..
        /// </summary>
        public static string Account_ContactNotFound {
            get {
                return ResourceManager.GetString("Account_ContactNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to delete account..
        /// </summary>
        public static string Account_DeleteFailed {
            get {
                return ResourceManager.GetString("Account_DeleteFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No addresses found for the contact..
        /// </summary>
        public static string Account_NoAddressesFound {
            get {
                return ResourceManager.GetString("Account_NoAddressesFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number was not updated properly..
        /// </summary>
        public static string Account_PhoneNotUpdated {
            get {
                return ResourceManager.GetString("Account_PhoneNotUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update name..
        /// </summary>
        public static string Account_UpdatedNameFailed {
            get {
                return ResourceManager.GetString("Account_UpdatedNameFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update email..
        /// </summary>
        public static string Account_UpdateEmailFailed {
            get {
                return ResourceManager.GetString("Account_UpdateEmailFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update phone number..
        /// </summary>
        public static string Account_UpdatePhoneFailed {
            get {
                return ResourceManager.GetString("Account_UpdatePhoneFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device ID must be provided..
        /// </summary>
        public static string Auth_DeviceIdRequired {
            get {
                return ResourceManager.GetString("Auth_DeviceIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number or SSN must be provided..
        /// </summary>
        public static string Auth_PhoneOrSsnRequired {
            get {
                return ResourceManager.GetString("Auth_PhoneOrSsnRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to add item to cart..
        /// </summary>
        public static string Cart_AddItemFailed {
            get {
                return ResourceManager.GetString("Cart_AddItemFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Audkenni login failed..
        /// </summary>
        public static string Login_AudkenniFailed {
            get {
                return ResourceManager.GetString("Login_AudkenniFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required Audkenni settings not found..
        /// </summary>
        public static string Login_AudkenniSettingsNotFound {
            get {
                return ResourceManager.GetString("Login_AudkenniSettingsNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HagarId authentication failed..
        /// </summary>
        public static string Login_HagarIdFailed {
            get {
                return ResourceManager.GetString("Login_HagarIdFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type in number.
        /// </summary>
        public static string Login_HagarIdStartAuthMessage {
            get {
                return ResourceManager.GetString("Login_HagarIdStartAuthMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User not found..
        /// </summary>
        public static string Login_UserNotFound {
            get {
                return ResourceManager.GetString("Login_UserNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authentication ID must be provided..
        /// </summary>
        public static string Register_AuthIdRequired {
            get {
                return ResourceManager.GetString("Register_AuthIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to create contact..
        /// </summary>
        public static string Register_ContactCreateFailed {
            get {
                return ResourceManager.GetString("Register_ContactCreateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email must be provided..
        /// </summary>
        public static string Register_EmailRequired {
            get {
                return ResourceManager.GetString("Register_EmailRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot remove your own share..
        /// </summary>
        public static string ShoppingList_CannotRemoveOwnShare {
            get {
                return ResourceManager.GetString("ShoppingList_CannotRemoveOwnShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact value is required..
        /// </summary>
        public static string ShoppingList_ContactValueRequired {
            get {
                return ResourceManager.GetString("ShoppingList_ContactValueRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to create shopping list..
        /// </summary>
        public static string ShoppingList_CreatedFailed {
            get {
                return ResourceManager.GetString("ShoppingList_CreatedFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shopping list ID is required..
        /// </summary>
        public static string ShoppingList_IdRequired {
            get {
                return ResourceManager.GetString("ShoppingList_IdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid contact method..
        /// </summary>
        public static string ShoppingList_InvalidContactMethod {
            get {
                return ResourceManager.GetString("ShoppingList_InvalidContactMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to add item to shopping list..
        /// </summary>
        public static string ShoppingList_ItemAddFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ItemAddFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item ID is required..
        /// </summary>
        public static string ShoppingList_ItemIdRequired {
            get {
                return ResourceManager.GetString("ShoppingList_ItemIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item name is required..
        /// </summary>
        public static string ShoppingList_ItemNameRequired {
            get {
                return ResourceManager.GetString("ShoppingList_ItemNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item not found in shopping list..
        /// </summary>
        public static string ShoppingList_ItemNotFound {
            get {
                return ResourceManager.GetString("ShoppingList_ItemNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to remove item from shopping list..
        /// </summary>
        public static string ShoppingList_ItemRemoveFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ItemRemoveFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update item in shopping list..
        /// </summary>
        public static string ShoppingList_ItemUpdateFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ItemUpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name is required..
        /// </summary>
        public static string ShoppingList_NameRequired {
            get {
                return ResourceManager.GetString("ShoppingList_NameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shopping list not found..
        /// </summary>
        public static string ShoppingList_NotFound {
            get {
                return ResourceManager.GetString("ShoppingList_NotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to remove the share..
        /// </summary>
        public static string ShoppingList_RemoveShareFailed {
            get {
                return ResourceManager.GetString("ShoppingList_RemoveShareFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to share shopping list..
        /// </summary>
        public static string ShoppingList_ShareFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ShareFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shopping list Share Id is required..
        /// </summary>
        public static string ShoppingList_ShareIdRequired {
            get {
                return ResourceManager.GetString("ShoppingList_ShareIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shopping list share not found..
        /// </summary>
        public static string ShoppingList_ShareNotFound {
            get {
                return ResourceManager.GetString("ShoppingList_ShareNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to find updated item in the response..
        /// </summary>
        public static string ShoppingList_UpdatedItemNotFound {
            get {
                return ResourceManager.GetString("ShoppingList_UpdatedItemNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update shopping list..
        /// </summary>
        public static string ShoppingList_UpdateFailed {
            get {
                return ResourceManager.GetString("ShoppingList_UpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh token expired..
        /// </summary>
        public static string Token_RefreshTokenExpired {
            get {
                return ResourceManager.GetString("Token_RefreshTokenExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh token not found..
        /// </summary>
        public static string Token_RefreshTokenNotFound {
            get {
                return ResourceManager.GetString("Token_RefreshTokenNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh token is required..
        /// </summary>
        public static string Token_RefreshTokenRequired {
            get {
                return ResourceManager.GetString("Token_RefreshTokenRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh token already used..
        /// </summary>
        public static string Token_RefreshTokenUsed {
            get {
                return ResourceManager.GetString("Token_RefreshTokenUsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User not authenticated..
        /// </summary>
        public static string UserNotAuthenticated {
            get {
                return ResourceManager.GetString("UserNotAuthenticated", resourceCulture);
            }
        }
    }
}
