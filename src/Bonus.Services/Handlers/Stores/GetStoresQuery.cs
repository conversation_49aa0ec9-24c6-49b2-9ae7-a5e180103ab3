using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Setup;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Shared.Configuration.Settings;
using Bonus.Shared.Helpers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Bonus.Services.Handlers.Stores;

public class GetStoresQuery(BonusContext bonusContext, ILSRetailAdapter lsRetailAdapter, IOptionsSnapshot<BonusApiSettings> bonusApiSettings)
    : IRequestHandler<GetStoresRequest, GetStoresResponse>
{
    public async Task<GetStoresResponse> Handle(GetStoresRequest request, CancellationToken cancellationToken)
    {
        var stores = await GetStores(request, cancellationToken);

        if (request.Filter.HasValue())
        {
            stores = stores
                .Where(x =>
                {
                    // TODO: If these values are empty - should we treat as matched or not matched?
                    var descriptionMatch = x.Description.HasValue() && x.Description!.ContainsIcelandicInvariant(request.Filter!);
                    var addressMatch = x.Address.Address1.HasValue() && x.Address.Address1!.ContainsIcelandicInvariant(request.Filter!);
                    var cityMatch = x.Address.City.HasValue() && x.Address.City!.ContainsIcelandicInvariant(request.Filter!);

                    return descriptionMatch || addressMatch || cityMatch;
                })
                .ToList();
        }
        
        var mappings = await bonusContext.StoreMappings.Select(x => new
        {
            x.LSRetailStoreId,
            x.EasyShopStoreId
        }).ToListAsync(cancellationToken);

        var responseStores = stores
            .Select(x =>
            {
                var distance = CalculateDistance(request.Latitude, request.Longitude, x.Latitude, x.Longitude);

                return new GetStoresListItem
                {
                    Id = x.Id,
                    Name = x.Description,
                    Distance = distance,
                    IsUserInStore = distance < 100,
                    Address = new GetStoresAddress
                    {
                        Street = x.Address.Address1,
                        City = x.Address.City,
                        PostalCode = x.Address.PostalCode,
                    },
                    Coordinates = new GetStoresCoordinates
                    {
                        Latitude = x.Latitude,
                        Longitude = x.Longitude
                    },
                    Phone = x.Phone,
                    IsClickAndCollect = x.IsClickAndCollect,
                    WorkingHours = x.StoreHours.Select(y => new GetStoresWorkingHours
                    {
                        DayOfWeek = y.DayOfWeek,
                        DayName = GetDayName(y.DayOfWeek), // TODO: Should we handle naming on backend? Or will localization be handled in-app?
                        OpenFrom = y.OpenFrom?.ToString("HH\\:mm"), // Format to "HH:mm"
                        OpenTo = y.OpenTo?.ToString("HH\\:mm") // Format to "HH:mm"
                    }).ToList(),
                    CartStoreId = mappings.FirstOrDefault(m => m.LSRetailStoreId == x.Id)?.EasyShopStoreId
                };
            })
            .OrderBy(x => x.Distance)
            .ToList();

        if (bonusApiSettings.Value.StoreNumberAlwaysInStore.HasValue())
        {
            var matchingStore = responseStores
                .Where(x => x.Id == bonusApiSettings.Value.StoreNumberAlwaysInStore)
                .FirstOrDefault();

            if (matchingStore is not null)
            {
                responseStores.ForEach(x => x.IsUserInStore = false);
                
                matchingStore.IsUserInStore = true;
            }
        }

        return new GetStoresResponse
        {
            Stores = responseStores
        };
    }

    /// <summary>
    /// Calculates the distance between two points on Earth using the Haversine formula.
    /// </summary>
    /// <returns>Distance in meters</returns>
    private static decimal? CalculateDistance(decimal? userLatitude, decimal? userLongitude, decimal storeLatitude, decimal storeLongitude)
    {
        // If user coordinates are not provided, return 0
        if (userLatitude == null || userLongitude == null)
        {
            return null;
        }

        const double earthRadiusKm = 6371.0d; // Earth's radius in kilometers

        // Convert decimal degrees to radians
        var lat1Rad = (double)userLatitude * Math.PI / 180.0;
        var lon1Rad = (double)userLongitude * Math.PI / 180.0;
        var lat2Rad = (double)storeLatitude * Math.PI / 180.0;
        var lon2Rad = (double)storeLongitude * Math.PI / 180.0;

        // Haversine formula
        var deltaLat = lat2Rad - lat1Rad;
        var deltaLon = lon2Rad - lon1Rad;

        var a = Math.Sin(deltaLat / 2) * Math.Sin(deltaLat / 2) + Math.Cos(lat1Rad) * Math.Cos(lat2Rad) * Math.Sin(deltaLon / 2) * Math.Sin(deltaLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        var distanceKm = earthRadiusKm * c;

        // Convert to meters and return as decimal
        return (decimal)(distanceKm * 1000);
    }

    private static string GetDayName(int dayOfWeek)
    {
        return dayOfWeek switch
        {
            1 => "Monday",
            2 => "Tuesday",
            3 => "Wednesday",
            4 => "Thursday",
            5 => "Friday",
            6 => "Saturday",
            0 => "Sunday",
            _ => $"-" // TODO: Should we handle LS Retail errors?
        };
    }

    private async Task<List<Store>> GetStores(GetStoresRequest request, CancellationToken cancellationToken)
    {
        if (request is { Latitude: not null, Longitude: not null })
        {
            var coordinatesApiRequest = new LSGetStoresByCoordinatesRequest
            {
                Latitude = request.Latitude.Value,
                Longitude = request.Longitude.Value,
                MaxDistance = request.MaxDistance ?? 150
            };

            var storesByCoordinatesResponse = await lsRetailAdapter.GetStoresByCoordinates(coordinatesApiRequest, cancellationToken);
            return storesByCoordinatesResponse.Response!.Stores;
        }

        var getAllApiRequest = new LSGetStoresRequest
        {
            StoreGetType = StoreGetType.All,
            IncludeDetails = true,
            IncludeImages = false
        };

        var storesResponse = await lsRetailAdapter.GetStores(getAllApiRequest, cancellationToken);
        return storesResponse.Response!.Stores;
    }
}

public class GetStoresRequest : IRequest<GetStoresResponse>
{
    public string? Filter { get; set; }

    public decimal? Latitude { get; set; }

    public decimal? Longitude { get; set; }

    public decimal? MaxDistance { get; set; }
}

public class GetStoresResponse
{
    public List<GetStoresListItem> Stores { get; set; } = [];
}

public class GetStoresListItem
{
    public required string Id { get; set; }

    public required string? Name { get; set; }

    public required GetStoresAddress Address { get; set; }

    public required GetStoresCoordinates Coordinates { get; set; }

    public required List<GetStoresWorkingHours> WorkingHours { get; set; } = [];

    public required string? Phone { get; set; } // TODO: Should phone be prefixed with country code?

    public required bool IsClickAndCollect { get; set; }

    public decimal? Distance { get; set; }

    public bool IsUserInStore { get; set; }
    
    public required string? CartStoreId { get; set; }
}

public class GetStoresWorkingHours
{
    public required int DayOfWeek { get; set; }

    public required string DayName { get; set; }

    public required string? OpenFrom { get; set; }

    public required string? OpenTo { get; set; }
}

public class GetStoresAddress
{
    public required string? Street { get; set; }

    public required string? City { get; set; }

    public required string? PostalCode { get; set; }
}

public class GetStoresCoordinates
{
    public required decimal Latitude { get; set; }

    public required decimal Longitude { get; set; }
}