using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public class GetAccountQuery(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser, ILogger<GetAccountQuery> logger) : IRequestHandler<GetAccountRequest, GetAccountResponse>
{


    public async Task<GetAccountResponse> Handle(GetAccountRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId == null)
        {
            logger.LogWarning("User not authenticated or CardId not available");
            throw new BonusException("User not authenticated!");
        }

        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

        if (contactResponse.Response?.Contact is null)
        {
            logger.LogWarning("Contact not found for CardId: {CardId}", bonusUser.CardId);
            throw new BonusException("Contact not found for CardId: {CardId}", bonusUser.CardId);
        }

        var contact = contactResponse.Response!.Contact;
        var phoneNumber = contact.Addresses
            .Select(a => a.PhoneNumber)
            .FirstOrDefault(p => !string.IsNullOrEmpty(p)) ?? string.Empty;

        //TODO: remove dummy data
        return new GetAccountResponse
        {
            UserId = bonusUser.UserId,
            FirstName = contact.FirstName ?? "Unknown",
            LastName = contact.LastName ?? "User",
            Email = string.IsNullOrEmpty(contact.Email) ? "<EMAIL>" : contact.Email,
            PhoneNumber = string.IsNullOrEmpty(phoneNumber) ? "********" : phoneNumber,
            CardId = bonusUser.CardId
        };
    }
}

public class GetAccountRequest : IRequest<GetAccountResponse>
{
}

public class GetAccountResponse
{
    public int UserId { get; set; }
    
    public string FirstName { get; set; } = string.Empty;
    
    public string LastName { get; set; } = string.Empty;
    
    public string Email { get; set; } = string.Empty;
    
    public string PhoneNumber { get; set; } = string.Empty;
    
    public string CardId { get; set; } = string.Empty;
}