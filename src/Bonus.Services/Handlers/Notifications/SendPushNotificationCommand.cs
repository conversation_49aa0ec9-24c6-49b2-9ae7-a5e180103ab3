using Bonus.Adapters.Expo;
using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Notifications;

public class SendPushNotificationCommand(IExpoPushServiceApi expoPushApi, BonusContext bonusContext, ISystemTime systemTime)
    : IRequestHandler<SendPushNotificationRequest>
{
    public async Task Handle(SendPushNotificationRequest request, CancellationToken cancellationToken)
    {
        var notification = await bonusContext.Notifications.Where(n => n.Id == request.NotificationId)
            .FirstOrDefaultAsync(cancellationToken);

        if (notification == null)
        {
            throw new BonusException("Notification not found!");
        }

        var pushTokens = notification.Recipients
            .Where(r => !string.IsNullOrEmpty(r.User.UserExternalNotificationId))
            .Where(r => r.User.ArchivedTime == null)
            .Select(r => r.User.UserExternalNotificationId!)
            .ToList();

        if (!pushTokens.Any())
        {
            throw new BonusException("No push tokens found for users");
        }
    
        var pushNotification = new PushNotification
        {
            Title = notification.Title,
            Body = notification.Description,
            Recipients = pushTokens,
            Priority = PushNotification.NotificationPriority.Default
        };

        var response = await expoPushApi.SendAsync(pushNotification);

        if (response.IsSuccessStatusCode)
        {
            notification.SentAt = systemTime.UtcNow;
            await bonusContext.SaveChangesAsync(cancellationToken);

            return;
        }

        throw new BonusException("Failed to send push notification: " + response.Error?.Content ?? "Unknown error");

    }
}

public class SendPushNotificationRequest : IRequest
{
    public required int NotificationId { get; set; }
}
