using Bonus.Core.Data;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Notifications;

public class GetNotificationQuery(BonusContext dbContext, IBonusUser bonusUser)
    : IRequestHandler<GetNotificationRequest, GetNotificationResponse>
{
    public async Task<GetNotificationResponse> Handle(GetNotificationRequest request, CancellationToken cancellationToken)
    {
        var notification = await dbContext.NotificationRecipients
            .Where(nr => nr.Id == request.Id)
            .Where(nr => nr.UserId == bonusUser.UserId)
            .Where(nr => nr.Notification.ArchivedTime == null)
            .Select(nr => new NotificationDto
            {
                Id = nr.Id.ToString(),
                Title = nr.Notification.Title,
                Description = nr.Notification.Description,
                CreatedAt = nr.Notification.CreatedTime.ToString("o"),
                Read = nr.ReadTime != null
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (notification == null)
        {
            throw new BonusException("Notification not found");
        }

        return new GetNotificationResponse
        {
            Notification = notification
        };
    }
}

public class GetNotificationRequest : IRequest<GetNotificationResponse>
{
    public required int Id { get; set; }
}

public class GetNotificationResponse
{
    public required NotificationDto Notification { get; set; }
}
