using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Notifications;

public class GetNotificationByIdQuery(BonusContext bonusContext, IBonusUser bonusUser)
    : IRequestHandler<GetNotificationByIdRequest, GetNotificationByIdResponse>
{
    public async Task<GetNotificationByIdResponse> Handle(GetNotificationByIdRequest request, CancellationToken cancellationToken)
    {
        var notification = await bonusContext.NotificationRecipients
            .Where(nr => nr.Id.ToString() == request.Id)
            .Where(nr => nr.UserId == bonusUser.UserId)
            .Select(nr => new NotificationDto
            {
                Id = nr.Id.ToString(),
                Title = nr.Notification.Title,
                Description = nr.Notification.Description,
                CreatedAt = nr.Notification.CreatedTime.ToString("o"),
                Read = nr.ReadTime != null
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (notification == null)
        {
            throw new BonusException("Notification not found");
        }

        return new GetNotificationByIdResponse
        {
            Notification = notification
        };
    }
}

public class GetNotificationByIdRequest : IRequest<GetNotificationByIdResponse>
{
    public required string Id { get; set; }
}

public class GetNotificationByIdResponse
{
    public required NotificationDto Notification { get; set; }
}
