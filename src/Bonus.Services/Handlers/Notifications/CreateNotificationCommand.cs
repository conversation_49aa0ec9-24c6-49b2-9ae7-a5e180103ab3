using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;

namespace Bonus.Services.Handlers.Notifications;

public class CreateNotificationCommand(BonusContext context)
    : IRequestHandler<CreateNotificationRequest>
{
    public async Task Handle(CreateNotificationRequest request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Title))
        {
            throw new BonusException("Title is required");
        }

        if (string.IsNullOrWhiteSpace(request.Body))
        {
            throw new BonusException("Body is required");
        }

        if (request.UserIds == null || !request.UserIds.Any())
        {
            throw new BonusException("At least one user ID is required");
        }

        var notification = new Notification()
        {
            Title = request.Title,
            Description = request.Body
        };

        await context.Notifications.AddAsync(notification, cancellationToken);

        foreach (var userId in request.UserIds)
        {
            notification.Recipients.Add(new NotificationRecipient()
            {
                UserId = userId,
                Notification = notification
            });
        }

        
        await context.SaveChangesAsync();
    }
}

public class CreateNotificationRequest : IRequest
{
    public required string Title { get; set; }
    
    public required string Body { get; set; }
    
    public required IEnumerable<int> UserIds { get; set; }
}