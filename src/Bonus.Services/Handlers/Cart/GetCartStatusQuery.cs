using Bonus.Adapters.EasyShop;
using Bonus.Adapters.EasyShop.Enums;
using Bonus.Adapters.EasyShop.Helpers;
using Bonus.Adapters.EasyShop.Models;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Cart;

public class GetCartStatusQuery(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter) : IRequestHandler<GetCartStatusRequest, GetCartStatusResponse>
{
    public async Task<GetCartStatusResponse> Handle(GetCartStatusRequest request, CancellationToken cancellationToken)
    {
        var easyShopRequest = new EasyShopGetEndShoppingTripStateRequest
        {
            StoreId = request.StoreId,
            CartId = request.CartId.HasValue() ? request.CartId : "0",
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = EasyShopHelper.AcceptLanguage,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var result = await easyShopAdapter.GetEndShoppingTripState(easyShopRequest, cancellationToken);

        if (result.Success is false)
        {
            throw new BonusException($"Failed to get cart status: {result.Error?.Message}");
        }

        return new GetCartStatusResponse
        {
            CartId = result.Response!.Id ?? request.CartId!,
            Status = MapShoppingTripStatus(result.Response!)
        };
    }

    private static string MapShoppingTripStatus(ESEndShoppingTripStateDto response) => response.ShoppingTripStatus switch
    {
        ShoppingTripStatus.WaitingForPayment when response.PaymentAllowedStatus is PaymentAllowedStatus.Allowed => "please_pay",
        ShoppingTripStatus.WaitingForPayment when response.PaymentAllowedStatus is PaymentAllowedStatus.Blocked => "control_failed",
        ShoppingTripStatus.WaitingForPayment when response.PaymentAllowedStatus is PaymentAllowedStatus.Pending or PaymentAllowedStatus.PendingExpired => "staff_check",
        ShoppingTripStatus.Completed => "payment_success",
        ShoppingTripStatus.Cancelled or ShoppingTripStatus.NoPayment or ShoppingTripStatus.Abandoned => "payment_failed",
        ShoppingTripStatus.Active => "active",
        _ => "unknown"
    };
}

public class GetCartStatusRequest : IRequest<GetCartStatusResponse>
{
    public required string? CartId { get; set; }
    
    public required string StoreId { get; set; }
}

public class GetCartStatusResponse
{
    public required string CartId { get; set; }

    public required string Status { get; set; }
}