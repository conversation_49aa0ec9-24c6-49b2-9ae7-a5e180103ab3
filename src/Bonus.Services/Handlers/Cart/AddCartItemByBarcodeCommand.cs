using Bonus.Adapters.EasyShop;
using Bonus.Adapters.EasyShop.Enums;
using Bonus.Adapters.EasyShop.Helpers;
using Bonus.Adapters.EasyShop.Models;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Cart;

public class AddCartItemByBarcodeCommand(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter)
    : IRequestHandler<AddCartItemByBarcodeRequest, AddCartItemByBarcodeResponse>
{
    public async Task<AddCartItemByBarcodeResponse> Handle(AddCartItemByBarcodeRequest request, CancellationToken cancellationToken)
    {
        var easyShopRequest = new EasyShopAddRemoveItemRequest
        {
            Body = new ESAddRemoveItemDto
            {
                Quantity = request.Quantity,
                Barcode = new ESBarcodeDto
                {
                    Data = request.Barcode,
                    Symbology = BarcodeSymbologyType.EAN13
                }
            },
            StoreId = request.StoreId,
            CartId = request.CartId,
            ShopperIdentifier = bonusUser.CardId,
            DeviceId = bonusUser.DeviceId,
            AcceptLanguage = EasyShopHelper.AcceptLanguage,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var result = await easyShopAdapter.AddRemoveItem(easyShopRequest, cancellationToken);

        if (!result.Success)
        {
            throw new BonusException($"Failed to add item: {result.Error?.Message}");
        }

        var addedItem = result.Response?.Items
            .Where(x => x.Article.Barcode.Data == request.Barcode)
            .OrderByDescending(x => x.Quantity)
            .FirstOrDefault();

        if (addedItem is null)
        {
            throw new BonusException(nameof(Translations.Cart_AddItemFailed));
        }

        // TODO: Other fields?
        return new AddCartItemByBarcodeResponse
        {
            ItemId = addedItem.Id,
            Name = addedItem.Article.Description,
            ImageUrl = "",
            Price = addedItem.Article.Price,
            Measure = addedItem.Article.UnitOfMeasure.ToString(),
            Quantity = addedItem.Quantity,
            DiscountPrice = null,
            DiscountPercentage = null,
            TotalPrice = addedItem.Total,
            CartTotalPrice = result.Response!.Total,
            CartTotalItems = result.Response!.Items.Count
        };
    }
}

public class AddCartItemByBarcodeRequest : IRequest<AddCartItemByBarcodeResponse>
{
    public required string CartId { get; set; }
    
    public required string StoreId { get; set; }
    
    public required string Barcode { get; set; }
    
    public required decimal Quantity { get; set; }
}

public class AddCartItemByBarcodeResponse
{
    public required string ItemId { get; set; }
    
    public required string Name { get; set; }
    
    public required string ImageUrl { get; set; }
    
    public required decimal Price { get; set; }
    
    public required string Measure { get; set; }
    
    public required decimal Quantity { get; set; }
    
    public required decimal? DiscountPrice { get; set; }
    
    public required int? DiscountPercentage { get; set; }
    
    public required decimal TotalPrice { get; set; }
    
    public required decimal CartTotalPrice { get; set; }
    
    public required int CartTotalItems { get; set; }
}