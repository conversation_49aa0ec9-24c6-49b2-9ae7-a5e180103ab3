using Bonus.Adapters.EasyShop;
using Bonus.Adapters.EasyShop.Helpers;
using Bonus.Adapters.EasyShop.Models;
using Bonus.Adapters.EasyShop.Refit;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Cart;

public class GetCartQuery(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter) : IRequestHandler<GetCartRequest, GetCartResponse>
{
    public async Task<GetCartResponse> Handle(GetCartRequest request, CancellationToken cancellationToken)
    {
        var easyShopExistingCartRequest = new EasyShopGetShoppingTripRequest
        {
            StoreId = request.StoreId,
            CartId = request.CartId.HasValue() ? request.CartId : "0",
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = EasyShopHelper.AcceptLanguage,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var getCartResult = await easyShopAdapter.GetShoppingTrip(easyShopExistingCartRequest, cancellationToken);

        if (getCartResult.Success)
        {
            return await CreateResponse(getCartResult.Response!);
        }

        if (getCartResult.Error?.Type is not ESErrorTypes.ShoppingTripNotFound)
        {
            throw new BonusException($"Failed to get cart: {getCartResult.Error?.Message}");
        }
            
        // If the cart is not found, we create a new one.
        var easyShopCreateCartRequest = new EasyShopBeginShoppingTripRequest
        {
            StoreId = request.StoreId,
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = EasyShopHelper.AcceptLanguage,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString(),
            CartId = null,
            Body = new()
        };
            
        var createResult = await easyShopAdapter.BeginShoppingTrip(easyShopCreateCartRequest, cancellationToken);
        if (createResult.Success is false)
        {
            throw new BonusException($"Failed to create cart: {createResult.Error?.Message}");
        }
            
        return await CreateResponse(createResult.Response!); 
    }

    private async Task<GetCartResponse> CreateResponse(ESShoppingTripDto trip)
    {
        var cartItems = trip.Cart.Items
            .Select(x => new CartItem
            {
                Id = x.Id,
                ItemId = x.Article.Barcode.Data,
                Name = x.Article.Description,
                ImageUrl = "", // TODO: where to get LS retail Id from?
                Price = x.Article.Price,
                Measure = x.Article.UnitOfMeasure.ToString(),
                Quantity = x.Quantity,
                DiscountPrice = null,
                DiscountPercentage = null,
                TotalPrice = x.Total
            })
            .ToList() ?? [];
        
        return new GetCartResponse
        {
            Id = trip.Id,
            Items = cartItems,
            TotalPrice = trip.Cart.Total,
            TotalItems = cartItems.Count,
            TotalDiscount = trip.Cart.TotalDiscount,
            LastModified = DateTime.UtcNow,
            QrDataMatrix = $"${trip.Cart.Id}"
        };
    }
}

public class GetCartRequest : IRequest<GetCartResponse>
{
    public required string? CartId { get; set; }
    
    public required string StoreId { get; set; }
}

public class GetCartResponse
{
    public required string Id { get; set; }
    
    public required List<CartItem> Items { get; set; } = [];
    
    public required decimal TotalPrice { get; set; }
    
    public required int TotalItems { get; set; }
    
    public required decimal TotalDiscount { get; set; }
    
    public required DateTime LastModified { get; set; }

    public required string QrDataMatrix { get; set; }
}

public class CartItem
{
    public required string Id { get; set; }
    
    public required string ItemId { get; set; }
    
    public required string Name { get; set; }
    
    public required string ImageUrl { get; set; }
    
    public required decimal Price { get; set; }
    
    public required string Measure { get; set; }
    
    public required decimal Quantity { get; set; }
    
    public decimal? DiscountPrice { get; set; }
    
    public int? DiscountPercentage { get; set; }
    
    public required decimal TotalPrice { get; set; }
}