using Bonus.Adapters.EasyShop;
using Bonus.Adapters.EasyShop.Enums;
using Bonus.Adapters.EasyShop.Helpers;
using Bonus.Adapters.EasyShop.Models;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Cart;

public class CheckoutCartCommand(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter)
    : IRequestHandler<CheckoutCartRequest, CheckoutCartResponse>
{
    public async Task<CheckoutCartResponse> Handle(CheckoutCartRequest request, CancellationToken cancellationToken)
    {
        var getTripRequest = new EasyShopGetShoppingTripRequest()
        {
            StoreId = request.StoreId,
            CartId = request.CartId,
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = EasyShopHelper.AcceptLanguage,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };
        
        var tripResult = await easyShopAdapter.GetShoppingTrip(getTripRequest, cancellationToken);
        
        if (!tripResult.Success)
        {
            throw new BonusException($"Failed to get shopping trip: {tripResult.Error?.Message}");
        }

        if (request.Barcode.HasValue() && request.Barcode!.Contains(tripResult.Response!.EndShoppingTripBarcode) is false)
        {
            throw new BonusException($"Provided barcode {request.Barcode} does not match the end shopping trip barcode {tripResult.Response.EndShoppingTripBarcode}.");
        }
        
        var easyShopRequest = new EasyShopEndShoppingTripRequest
        {
            StoreId = request.StoreId,
            CartId = request.CartId,
            Body = new ESEndShoppingTripDto
            {
                MissingItems = request.MissingItems,
                ExternalEndOfTrip = request.ExternalEndOfTrip,
                Barcode = request.Barcode.HasValue() ? new ESBarcodeDto()
                {
                    Data = request.Barcode!,
                    Symbology = BarcodeSymbologyType.EAN13
                } : null,
            },
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = EasyShopHelper.AcceptLanguage,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var result = await easyShopAdapter.EndShoppingTrip(easyShopRequest, cancellationToken);

        if (!result.Success)
        {
            throw new BonusException($"Failed to checkout cart: {result.Error?.Message}");
        }
        
        // TODO: Expose EndShoppingTripBarcode? Should we use PaymentBlockedReason anyhow?

        var checkoutItems = result.Response?.Cart.Items
            .Select(x => new CheckoutCartItem
            {
                Id = x.Id,
                ItemId = x.Article.Barcode.Data,
                Name = x.Article.Description,
                ImageUrl = "",
                Price = x.Article.Price,
                Measure = x.Article.UnitOfMeasure.ToString(),
                Quantity = (int)x.Quantity,
                DiscountPrice = null,
                DiscountPercentage = null,
                TotalPrice = x.Total
            })
            .ToList();

        var paymentStatus = result.Response!.PaymentAllowedStatus switch
        {
            PaymentAllowedStatus.Allowed => "Allowed",
            PaymentAllowedStatus.Blocked => "Blocked",
            PaymentAllowedStatus.Pending => "Pending",
            _ => "Unknown"
        };

        return new CheckoutCartResponse
        {
            OrderId = result.Response!.Id,
            TotalAmount = result.Response!.Cart.Total,
            TotalDiscount = result.Response!.Cart.TotalDiscount,
            TotalItems = checkoutItems?.Sum(x => x.Quantity) ?? 0,
            OrderDate = DateTime.UtcNow,
            PaymentStatus = paymentStatus,
            PaymentReference = result.Response!.PaymentReference,
            Items = checkoutItems ?? []
        };
    }
}

public class CheckoutCartRequest : IRequest<CheckoutCartResponse>
{
    public required string CartId { get; set; }
    
    public required string StoreId { get; set; }
    
    public required bool MissingItems { get; set; }
    
    public required bool ExternalEndOfTrip { get; set; }
    
    public string? Barcode { get; set; }
}

public class CheckoutCartResponse
{
    public required string OrderId { get; set; }
    
    public required decimal TotalAmount { get; set; }
    
    public required decimal TotalDiscount { get; set; }
    
    public required int TotalItems { get; set; }
    
    public required DateTime OrderDate { get; set; }
    
    public required string PaymentStatus { get; set; }
    
    public string? PaymentReference { get; set; }
    
    public required List<CheckoutCartItem> Items { get; set; } = [];
}

public class CheckoutCartItem
{
    public required string Id { get; set; }
    
    public required string ItemId { get; set; }
    
    public required string Name { get; set; }
    
    public required string ImageUrl { get; set; }
    
    public required decimal Price { get; set; }
    
    public required string Measure { get; set; }
    
    public required int Quantity { get; set; }
    
    public decimal? DiscountPrice { get; set; }
    
    public int? DiscountPercentage { get; set; }
    
    public required decimal TotalPrice { get; set; }
}