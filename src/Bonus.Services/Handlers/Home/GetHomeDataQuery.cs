using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Resources;
using Bonus.Services.Handlers.PurchaseHistory;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Home;

public class GetHomeDataQuery(ILSRetailAdapter lsRetailAdapter, IShoppingListService shoppingListService, IBonusUser bonusUser) 
    : IRequestHandler<GetHomeDataRequest, GetHomeDataResponse>
{
    public async Task<GetHomeDataResponse> Handle(GetHomeDataRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var user = await GetUserInfoAsync(cancellationToken);
        var shoppingLists = await shoppingListService.GetShoppingLists(cancellationToken);

        return new GetHomeDataResponse
        {
            ProfileName = user.FullName,
            QrCode = bonusUser.CardId ?? "N/A", // TODO: QR code should be generated based on CardId
            LatestPurchase = user.LastTransaction,
            ShoppingList = shoppingLists.FirstOrDefault(),
        };
    }
    
    private async Task<(string FullName, ResultPurchaseHistory? LastTransaction)> GetUserInfoAsync(CancellationToken cancellationToken)
    {
        var userRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 1
        };
        
        var userResponse = await lsRetailAdapter.GetContactByCardId(userRequest, cancellationToken);
        
        if (userResponse.Success is false || userResponse.Response!.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }
        
        var user = userResponse.Response.Contact;
        
        var lastTransaction = userResponse.Response.Contact.SalesEntries.Select(x => new ResultPurchaseHistory
        {
            Id = x.Id!,
            PurchaseDate = x.TransactionDate,
            TotalAmount = x.NetAmount,
            Title = x.StoreName!,
            QrCode = x.DocumentNo!, // TODO: implement QR code generation,
            Items = []
        }).FirstOrDefault();
        
        return (user.Name!, lastTransaction);
    }
}

public class GetHomeDataRequest : IRequest<GetHomeDataResponse>;

public class GetHomeDataResponse
{
    public required string ProfileName { get; set; }

    public required string QrCode { get; set; }

    public required ResultShoppingList? ShoppingList { get; set; }

    public required ResultPurchaseHistory? LatestPurchase { get; set; }
}