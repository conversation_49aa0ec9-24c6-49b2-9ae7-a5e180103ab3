using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Services.Services;
using MediatR;

namespace Bonus.Services.Handlers.PurchaseHistory;

public class GetPurchaseHistoryDetailsQuery(ILSRetailAdapter lsRetailAdapter, IImageUrlService imageUrlService)
    : IRequestHandler<GetPurchaseHistoryDetailsRequest, GetPurchaseHistoryDetailsResponse>
{
    public async Task<GetPurchaseHistoryDetailsResponse> Handle(GetPurchaseHistoryDetailsRequest request, CancellationToken cancellationToken)
    {
        var saleEntryRequest = new LSGetSalesEntryRequest()
        {
            EntryId = request.Id,
            Type = DocumentIdType.Order,
        };

        var salesEntriesResponse = await lsRetailAdapter.GetSalesEntry(saleEntryRequest, cancellationToken);

        var entry = salesEntriesResponse.Response!.SalesEntry;
        var response = new GetPurchaseHistoryDetailsResponse
        {
            SalesEntry = new ResultPurchaseHistory
            {
                Id = entry.Id,
                PurchaseDate = entry.DocumentDate.UtcDateTime,
                TotalAmount = entry.TotalAmount,
                Title = entry.StoreName,
                QrCode = entry.ReceiptNumber, // TODO: implement QR code generation,
                Items = entry.Lines.Select(line => new PurchaseHistoryItemDetail
                {
                    Id = line.Id,
                    ItemId = line.ItemId,
                    Name = line.ItemDescription,
                    Quantity = line.Quantity,
                    Price = line.Price,
                    ImageUrl = imageUrlService.CreateImageUrl(line.ItemId)
                }).ToList(),
            }
        };
        
        return response;
    }
}

public class GetPurchaseHistoryDetailsRequest : IRequest<GetPurchaseHistoryDetailsResponse>
{
    public required string Id { get; set; }
}

public class GetPurchaseHistoryDetailsResponse
{
    public required ResultPurchaseHistory SalesEntry { get; set; }
}