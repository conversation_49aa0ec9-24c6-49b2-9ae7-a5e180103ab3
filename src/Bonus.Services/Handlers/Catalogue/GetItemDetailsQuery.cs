using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;

namespace Bonus.Services.Handlers.Catalogue;

public class GetItemDetailsQuery(ILSRetailAdapter lsRetailAdapter, IImageUrlService imageUrlService)
    : IRequestHandler<GetItemDetailsRequest, GetItemDetailsResponse>
{
    public async Task<GetItemDetailsResponse> Handle(GetItemDetailsRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetItemGetByIdRequest
        {
            ItemId = request.Id,
            StoreId = "", // TODO: Implement store selection
        };

        var itemResponse = await lsRetailAdapter.GetItemById(apiRequest, cancellationToken);

        if (itemResponse.Response?.Item is null)
        {
            throw new BonusException($"Item with ID {request.Id} not found");
        }

        var item = itemResponse.Response!.Item;
        var selectedUnitOfMeasure = item.UnitOfMeasures.Where(x => x.ItemId == item.Id).OrderBy(x => x.QtyPerUom).FirstOrDefault();

        var response = new GetItemDetailsResponse
        {
            Id = item.Id,
            Name = item.Description ?? "N/A",
            Description = item.Details ?? "N/A",
            Price = item.Prices.FirstOrDefault()?.Amount ?? 0,
            Measure = selectedUnitOfMeasure?.Id ?? "N/A",
            PricePerUnit = item.Prices.Find(x => x.UomId == selectedUnitOfMeasure?.Id)?.Amount ?? 0,
            UnitType = selectedUnitOfMeasure?.ShortDescription ?? "N/A",
            ImageUrls = item.Images
                .Select(x => new
                {
                    x.DisplayOrder,
                    Url = imageUrlService.CreateImageUrl(x.Id)
                })
                .Where(x => x.Url.HasValue())
                .OrderBy(x => x.DisplayOrder)
                .Select(x => x.Url)
                .ToList(),
            DiscountedPrice = null, // TODO: where from?
            DiscountPercentage = null, // TODO: where from?
            Downsell = null,  // TODO: where from?
            NutritionalBadges = [], // TODO: where from?
            CountryOfOrigin = null, // TODO: where from?
            Ingredients = null, // TODO: where from?
            NutritionalValues = [] // TODO: where from?
        };

        return response;
    }
}

public class GetItemDetailsRequest : IRequest<GetItemDetailsResponse>
{
    public required string Id { get; set; }
}

public class GetItemDetailsResponse
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string Description { get; set; }

    public required List<string> ImageUrls { get; set; } = [];

    public required decimal Price { get; set; }

    public decimal? DiscountedPrice { get; set; }

    public decimal? DiscountPercentage { get; set; }

    public DownsellItem? Downsell { get; set; }

    public required string Measure { get; set; }

    public required decimal PricePerUnit { get; set; }

    public required string UnitType { get; set; } // "kg", "l", etc.

    public required List<NutritionalBadge> NutritionalBadges { get; set; } = [];

    public string? CountryOfOrigin { get; set; }

    public string? Ingredients { get; set; }

    public required List<NutritionalValue> NutritionalValues { get; set; } = [];
}

public class DownsellItem
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string Description { get; set; }

    public required List<string> ImageUrls { get; set; } = [];

    public required decimal Price { get; set; }

    public required string Measure { get; set; }
}

public class NutritionalBadge
{
    public required string Name { get; set; }

    public required string Description { get; set; }

    public required string ImageUrl { get; set; }
}

public class NutritionalValue
{
    public required string Name { get; set; }

    public required string Value { get; set; }
}