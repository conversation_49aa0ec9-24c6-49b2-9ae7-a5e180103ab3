using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Handlers.Catalogue;

public class GetCatalogueImageQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetCatalogueImageRequest, Stream>
{
    public async Task<Stream> Handle(GetCatalogueImageRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetImageStreamByIdRequest()
        {
            Id = request.Id,
            Width = request.Width,
            Height = request.Height
        };

        var stream = await lsRetailAdapter.GetImageStreamById(apiRequest, cancellationToken);

        return stream.Response!;
    }
}

public class GetCatalogueImageRequest : IRequest<Stream>
{
    public required string Id { get; set; }

    public required int Width { get; set; }

    public required int Height { get; set; }
}