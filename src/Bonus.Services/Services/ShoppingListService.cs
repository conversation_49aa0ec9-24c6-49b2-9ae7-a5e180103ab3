using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Baskets;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Services;

public interface IShoppingListService
{
    Task<ResultShoppingList> GetShoppingList(string id, CancellationToken cancellationToken);
    
    Task<List<ResultShoppingList>> GetShoppingLists(CancellationToken cancellationToken);
    
    Task<ResultShoppingList> CreateShoppingList(string name, List<CreateShoppingListItem> items, CancellationToken cancellationToken);
    
    Task<ResultShoppingList> AddShoppingListItems(string id, List<CreateShoppingListItem> item, CancellationToken cancellationToken);
    
    Task<ResultShoppingList> AddShoppingListItem(string id, CreateShoppingListItem item, CancellationToken cancellationToken);
    
    Task<ResultShoppingList> UpdateShoppingList(string id, string name, CancellationToken cancellationToken);

    Task<ResultShoppingList> UpdateShoppingListItem(string shoppingListId, string id, UpdateShoppingListItem updateRequest, CancellationToken cancellationToken);

    Task<ResultShoppingList> ShareShoppingList(string id, ContactSearchType method, string contactValue, CancellationToken cancellationToken);

    Task<bool> RemoveShoppingList(string id, CancellationToken cancellationToken);

    Task<ResultShoppingList> RemoveShoppingListShare(string id, string shareId, CancellationToken cancellationToken);

    Task<ResultShoppingList> RemoveShoppingListItems(string id, List<string> itemIds, CancellationToken cancellationToken);
    
    Task<ResultShoppingList> CompleteShoppingListItems(string id, List<string> itemIds, bool completedFlag, CancellationToken cancellationToken);
}

public class ShoppingListService(BonusContext bonusContext, ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser, ISystemTime systemTime, IImageUrlService imageUrlService) : IShoppingListService
{
    public async Task<ResultShoppingList> GetShoppingList(string id, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        return await CreateResponse(list, cancellationToken);
    }

    public async Task<List<ResultShoppingList>> GetShoppingLists(CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByCardIdRequest
        {
            CardId = bonusUser.CardId,
            ListType = ListType.Wish,
            IncludeLines = true
        };

        var apiResponse = await lsRetailAdapter.GetOneListByCardId(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneLists is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var shoppingListIds = apiResponse.Response.OneLists.Select(x => x.Id).ToList();
        
        var dbShares = await bonusContext.ShoppingListShares
            .Where(x => shoppingListIds.Contains(x.ShoppingListId))
            .Select(x => new ShoppingListShareRecord(x.ShareId, x.ShoppingListId, x.LinkedCardId))
            .ToListAsync(cancellationToken);

        var listsWithShares = apiResponse.Response.OneLists.Select(x => new
        {
            x.Id,
            CardIds = x.CardLinks.Select(y => y.CardId).ToList()
        });
        
        foreach (var list in listsWithShares)
        {
            var existingListShares = dbShares.Where(x => x.ShoppingListId == list.Id).ToList();
            foreach (var cardId in list.CardIds)
            {
                if (existingListShares.Any(x => x.CardId == cardId))
                {
                    continue;
                }
                
                var newDbShare = new ShoppingListShare()
                {
                    ShareId = Guid.NewGuid().ToString(),
                    ShoppingListId = list.Id,
                    LinkedCardId = cardId
                };
                
                await bonusContext.ShoppingListShares.AddAsync(newDbShare);
            }
        }
        
        await bonusContext.SaveChangesAsync(cancellationToken);
        
        var dbItems = await bonusContext.ShoppingListItems
            .Where(x => shoppingListIds.Contains(x.ShoppingListId))
            .Select(x => new ShoppingListItemRecord(x.ShoppingListId, x.ItemDescription, x.Completed))
            .ToListAsync(cancellationToken);
        
        var oneListResult = new List<ResultShoppingList>();
        
        foreach (var list in apiResponse.Response.OneLists)
        {
            oneListResult.Add(await CreateResponse(list, dbItems, cancellationToken));
        }
        
        return oneListResult;
    }
    
    public async Task<ResultShoppingList> CreateShoppingList(string name, List<CreateShoppingListItem> items, CancellationToken cancellationToken)
    {
        var oneList = new OneList
        {
            Id = null!,
            CardId = bonusUser.CardId,
            Description = name,
            ListType = ListType.Wish,
            Items = [],
            CardLinks = [],
            CreateDate = systemTime.UtcNow,
        };

        foreach (var item in items)
        {
            var oneListItem = new OneListItem
            {
                Id = null!,
                ItemId = item.ItemId,
                ItemDescription = item.Name,
                Quantity = item.Quantity > 0 ? item.Quantity : 1, // Ensure minimum quantity of 1
                IsManualItem = item.ItemId.HasValue() is false, // If no ItemId is provided, it's a manual item
                CreateDate = systemTime.UtcNow,
            };

            oneList.Items.Add(oneListItem);
        }
        
        var saveRequest = new LSSaveOneListRequest
        {
            OneList = oneList,
            Calculate = false,
        };

        var saveResponse = await lsRetailAdapter.SaveOneList(saveRequest, cancellationToken);
        
        if (saveResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_CreatedFailed));
        }

        var list = saveResponse.Response.OneList;
        
        var dbShare = new ShoppingListShare
        {
            ShareId = Guid.NewGuid().ToString(),
            ShoppingListId = list.Id,
            LinkedCardId = bonusUser.CardId,
        };
        
        await bonusContext.ShoppingListShares.AddAsync(dbShare, cancellationToken);
        await bonusContext.SaveChangesAsync(cancellationToken);

        return await CreateResponse(list, cancellationToken);
    }

    public async Task<ResultShoppingList> AddShoppingListItem(string id, CreateShoppingListItem item, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        var existingItem = list.Items.FirstOrDefault(x => x.ItemDescription == item.Name && x.ItemId == item.ItemId);

        if (existingItem is not null)
        {
            existingItem.Quantity += item.Quantity > 0 ? item.Quantity : 1; // Ensure minimum quantity of 1
        }
        else
        {
            var oneListItem = new OneListItem
            {
                Id = null!,
                ItemId = item.ItemId,
                ItemDescription = item.Name,
                Quantity = item.Quantity > 0 ? item.Quantity : 1, // Ensure minimum quantity of 1
                IsManualItem = item.ItemId.HasValue() is false, // If no ItemId is provided, it's a manual item
                CreateDate = systemTime.UtcNow,
            };
            
            list.Items.Add(oneListItem);
        }

        var saveRequest = new LSSaveOneListRequest
        {
            OneList = list,
            Calculate = true,
        };

        var saveResponse = await lsRetailAdapter.SaveOneList(saveRequest, cancellationToken);
        
        if (saveResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemAddFailed));
        }

        return await CreateResponse(saveResponse.Response.OneList, cancellationToken);
    }
    
    public async Task<ResultShoppingList> AddShoppingListItems(string id, List<CreateShoppingListItem> items, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        foreach (var item in items)
        {
            var existingItem = list.Items.FirstOrDefault(x => x.ItemDescription == item.Name && x.ItemId == item.ItemId);

            if (existingItem is not null)
            {
                existingItem.Quantity += item.Quantity > 0 ? item.Quantity : 1; // Ensure minimum quantity of 1
            }
            else
            {
                var oneListItem = new OneListItem
                {
                    Id = null!,
                    ItemId = item.ItemId,
                    ItemDescription = item.Name,
                    Quantity = item.Quantity > 0 ? item.Quantity : 1, // Ensure minimum quantity of 1
                    IsManualItem = item.ItemId.HasValue() is false, // If no ItemId is provided, it's a manual item
                    CreateDate = systemTime.UtcNow,
                };
            
                list.Items.Add(oneListItem);
            }
        }

        var saveRequest = new LSSaveOneListRequest
        {
            OneList = list,
            Calculate = true,
        };

        var saveResponse = await lsRetailAdapter.SaveOneList(saveRequest, cancellationToken);
        
        if (saveResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemAddFailed));
        }

        return await CreateResponse(saveResponse.Response.OneList, cancellationToken);
    }

    public async Task<ResultShoppingList> UpdateShoppingList(string id, string name, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        list.Description = name;

        var saveRequest = new LSSaveOneListRequest
        {
            OneList = list,
        };

        var saveResponse = await lsRetailAdapter.SaveOneList(saveRequest, cancellationToken);

        if (saveResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_UpdateFailed));
        }
        
        return await CreateResponse(saveResponse.Response.OneList, cancellationToken);
    }

    public async Task<ResultShoppingList> UpdateShoppingListItem(string shoppingListId, string id, UpdateShoppingListItem updateRequest, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = shoppingListId,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        var itemToUpdate = list.Items.FirstOrDefault(x => x.Id == id);
        if (itemToUpdate is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemNotFound));
        }

        var oldItemName = itemToUpdate.ItemDescription;
        itemToUpdate.ItemDescription = updateRequest.Name;
        itemToUpdate.ItemId = updateRequest.ItemId;
        itemToUpdate.Quantity = updateRequest.Quantity > 0 ? updateRequest.Quantity.Value : 1; // Ensure minimum quantity of 1

        var saveRequest = new LSSaveOneListRequest
        {
            OneList = list,
            Calculate = true,
        };

        var saveResponse = await lsRetailAdapter.SaveOneList(saveRequest, cancellationToken);
        
        if (saveResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemUpdateFailed));
        }
        
        var dbItem = await bonusContext.ShoppingListItems
            .Where(x => x.ShoppingListId == shoppingListId && x.ItemDescription == oldItemName)
            .FirstOrDefaultAsync(cancellationToken);

        if (dbItem is not null)
        {
            dbItem.ItemDescription = itemToUpdate.ItemDescription; // Update the ShoppingListItemId to match the new item Description
        }

        return await CreateResponse(saveResponse.Response.OneList, cancellationToken);
    }

    public async Task<ResultShoppingList> ShareShoppingList(string id, ContactSearchType method, string contactValue, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var oldCardLinks = list.CardLinks.Select(x => x.CardId).ToList();
        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        var linkRequest = new LSLinkOneListRequest
        {
            OneListId = id,
            CardId = method is ContactSearchType.CardId ? contactValue : null,
            Email = method is ContactSearchType.Email ? contactValue : null,
            Phone = method is ContactSearchType.PhoneNumber ? contactValue : null,
            Status = LinkStatus.Requesting
        };

        var linkResponse = await lsRetailAdapter.LinkOneList(linkRequest, cancellationToken);
        
        // TODO: is this the proper handling? we discussed messages to register if user doesn't exist and stuff like that
        if (linkResponse.Success is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ShareFailed));
        }
        
        apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        list = apiResponse.Response.OneList;
        var newCardLinks = list.CardLinks.Select(x => x.CardId).ToList();

        var newestLink = newCardLinks.Except(oldCardLinks).FirstOrDefault();
        
        if (newestLink.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ShareFailed));
        }
        
        var dbShare = new ShoppingListShare
        {
            ShareId = Guid.NewGuid().ToString(),
            ShoppingListId = id,
            LinkedCardId = newestLink!,
        };

        await bonusContext.ShoppingListShares.AddAsync(dbShare, cancellationToken);
        await bonusContext.SaveChangesAsync(cancellationToken);
        
        return await CreateResponse(list, cancellationToken);
    }

    public async Task<bool> RemoveShoppingList(string id, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        var deleteRequest = new LSDeleteOneListByIdRequest
        {
            OneListId = id
        };

        var deleteResponse = await lsRetailAdapter.DeleteOneListById(deleteRequest, cancellationToken);

        if (deleteResponse.Success is false)
        {
            return false;
        }
        
        var dbShares = await bonusContext.ShoppingListShares
            .Where(x => x.ShoppingListId == id)
            .ToListAsync(cancellationToken);

        foreach (var share in dbShares)
        {
            share.ArchivedTime = systemTime.UtcNow;
        }
        
        await bonusContext.SaveChangesAsync(cancellationToken);
        return deleteResponse.Success;
    }

    public async Task<ResultShoppingList> RemoveShoppingListShare(string id, string shareId, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        var dbShare = await bonusContext.ShoppingListShares
            .Where(x => x.ShoppingListId == id && x.ShareId == shareId)
            .FirstOrDefaultAsync(cancellationToken);
        
        if (dbShare is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ShareNotFound));
        }
        
        var linkToRemove = list.CardLinks.FirstOrDefault(x => x.CardId == dbShare.LinkedCardId);
        if (linkToRemove is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ShareNotFound));
        }
        
        if (linkToRemove.CardId == bonusUser.CardId)
        {
            throw new BonusException(nameof(Translations.ShoppingList_CannotRemoveOwnShare));
        }
        
        list.CardLinks.Remove(linkToRemove);

        var linkRequest = new LSLinkOneListRequest
        {
            OneListId = list.Id,
            CardId = linkToRemove.CardId,
            Status = LinkStatus.Remove,
        };
        
        var linkOneList = await lsRetailAdapter.LinkOneList(linkRequest, cancellationToken);
        if (linkOneList.Success is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_RemoveShareFailed));
        }

        dbShare.ArchivedTime = systemTime.UtcNow;
        await bonusContext.SaveChangesAsync(cancellationToken);
        
        apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);
        
        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        list = apiResponse.Response.OneList;
        
        return await CreateResponse(list, cancellationToken);
    }

    public async Task<ResultShoppingList> RemoveShoppingListItems(string id, List<string> itemIds, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (userLinkExists is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        list.Items.RemoveAll(x => itemIds.Contains(x.Id));

        var saveRequest = new LSSaveOneListRequest
        {
            OneList = list,
            Calculate = false,
        };

        var saveResponse = await lsRetailAdapter.SaveOneList(saveRequest, cancellationToken);
        
        if (saveResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemRemoveFailed));
        }

        return await CreateResponse(saveResponse.Response.OneList, cancellationToken);
    }

    public async Task<ResultShoppingList> CompleteShoppingListItems(string id, List<string> itemIds, bool completedFlag, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = id,
            IncludeLines = true
        };
        
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);

        if (apiResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }

        var list = apiResponse.Response.OneList;

        var userLinkExists = list.CardLinks.Where(x => x.CardId == bonusUser.CardId).Any();
        if (!userLinkExists)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NotFound));
        }
        
        foreach (var item in list.Items)
        {
            if (item.ItemDescription.HasValue() is false)
            {
                continue; // Skip items without a description
            }
            
            var dbItem = await bonusContext.ShoppingListItems
                .Where(x => x.ShoppingListId == id && x.ItemDescription == item.ItemDescription)
                .FirstOrDefaultAsync(cancellationToken);

            var completed = itemIds.Contains(item.Id) ? completedFlag : false;
            
            if (dbItem is null)
            {
                dbItem = new ShoppingListItem
                {
                    ShoppingListId = id,
                    ItemDescription = item.ItemDescription!, // Cannot use item.Id as it changes on every one list save. Thanks LS Retail.
                    Completed = completed,
                };
                
                bonusContext.ShoppingListItems.Add(dbItem);
            }
            else
            {
                dbItem.Completed = completed;
            }
        }

        await bonusContext.SaveChangesAsync(cancellationToken);

        return await CreateResponse(list, cancellationToken);
    }
    
    private async Task<ResultShoppingList> CreateResponse(OneList list, CancellationToken cancellationToken)
    {
        var dbItems = await bonusContext.ShoppingListItems
            .Where(x => x.ShoppingListId == list.Id)
            .Select(x => new ShoppingListItemRecord(x.ShoppingListId, x.ItemDescription, x.Completed))
            .ToListAsync(cancellationToken);
        
        return await CreateResponse(list, dbItems, cancellationToken);
    }

    private async Task<ResultShoppingList> CreateResponse(OneList list, List<ShoppingListItemRecord> dbItems, CancellationToken cancellationToken)
    {
        var shares = await bonusContext.ShoppingListShares
            .Where(x => x.ShoppingListId == list.Id)
            .Select(x => new ShoppingListShareRecord(x.ShareId, x.ShoppingListId, x.LinkedCardId))
            .ToListAsync(cancellationToken);
        
        return await CreateResponse(list, dbItems, shares, cancellationToken);
    }

    private async Task<ResultShoppingList> CreateResponse(OneList list, List<ShoppingListItemRecord> dbItems, List<ShoppingListShareRecord> dbShares, CancellationToken cancellationToken)
    {
        var resultItems = list.Items.Select(x => new ResultShoppingListItem
        {
            Id = x.Id,
            Name = x.ItemDescription ?? "-",
            ItemId = x.ItemId,
            Description = x.ItemDescription,
            Price = x.Price,
            Quantity = x.Quantity,
            Measure = x.UnitOfMeasureId,
            ImageUrl = imageUrlService.CreateImageUrl(x.ItemId),
            IsCompleted = dbItems.Where(y => y.ShoppingListId == list.Id && y.ShoppingListItemDescription == x.ItemDescription).FirstOrDefault()?.Completed ?? false,
        }).ToList();
        
        var sharedWith = list.CardLinks
            .Where(link => link.CardId != bonusUser.CardId)
            .Select(link => new ResultShoppingListShare
            {
                Id = dbShares.Where(x => x.ShoppingListId == list.Id && x.CardId == link.CardId).FirstOrDefault()?.ShareId ?? "",
                Name = link.Name,
                IsAccepted = link.Status == LinkStatus.Active
            })
            .ToList();

        var isOwner = list.CardLinks
            .Where(link => link.CardId == bonusUser.CardId)
            .Select(link => (bool?)link.Owner)
            .FirstOrDefault() ?? true;

        var result = new ResultShoppingList
        {
            Id = list.Id,
            Name = list.Description ?? "-",
            Owner = isOwner,
            SharedWith = sharedWith,
            LastModified = list.CreateDate?.UtcDateTime ?? systemTime.UtcNow,
            Items = resultItems,
        };
        
        return result;
    }
}

public record CreateShoppingListItem(string Name, string? ItemId, decimal Quantity);

public record UpdateShoppingListItem(string Name, string? ItemId, decimal? Quantity);

public record ShoppingListItemRecord(string ShoppingListId, string ShoppingListItemDescription, bool Completed);

public record ShoppingListShareRecord(string ShareId, string ShoppingListId, string CardId);

public class ResultShoppingList
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required bool Owner { get; set; }

    public required List<ResultShoppingListItem> Items { get; set; } = [];
    
    public required List<ResultShoppingListShare> SharedWith { get; set; } = [];

    public required DateTime LastModified { get; set; }
    
    public int CompletedCount => Items.Count(x => x.IsCompleted);
    
    public int ItemCount => Items.Count;
    
    public decimal TotalPrice => Items.Sum(x => x.Price * x.Quantity); // Check how this is handled. Is the price here per unit or total price.
}

public class ResultShoppingListItem
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string? ItemId { get; set; }

    public required string? Description { get; set; }

    public required decimal Price { get; set; }

    public required decimal Quantity { get; set; }

    public required string? Measure { get; set; }

    public required string ImageUrl { get; set; }

    public required bool IsCompleted { get; set; }
}

public class ResultShoppingListShare
{
    public required string Id { get; set; }

    public required string? Name { get; set; }

    public required bool IsAccepted { get; set; }
}