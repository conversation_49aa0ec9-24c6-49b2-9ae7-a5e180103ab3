using Bonus.Adapters.LSRetail.Models.Hierarchies;
using Bonus.Shared.Types.Common.Exceptions;

namespace Bonus.Services.Services;

public interface ICatalogueService
{
    Task<List<CategoryData>> GetCategoriesAsync(CancellationToken cancellationToken = default);
    Task<CategoryData> GetCategoryByIdAsync(string id, CancellationToken cancellationToken = default);
}

public class CatalogueService(IHierarchyRawDataService hierarchyRawDataService, IImageUrlService imageUrlService) : ICatalogueService
{
    public async Task<List<CategoryData>> GetCategoriesAsync(CancellationToken cancellationToken = default)
    {
        var hierarchyResponse = await hierarchyRawDataService.GetHierarchyAsync(cancellationToken);

        var categories = ParseCategories(hierarchyResponse.Hierarchy.FirstOrDefault()?.Nodes ?? []);

        return categories;
    }

    public async Task<CategoryData> GetCategoryByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        var hierarchyResponse = await hierarchyRawDataService.GetHierarchyAsync(cancellationToken);

        var nodes = hierarchyResponse.Hierarchy.FirstOrDefault()?
            .Nodes.SelectMany(x => x.Nodes).Union(hierarchyResponse.Hierarchy.FirstOrDefault()?.Nodes ?? [])
            .ToList() ?? [];

        var category = ParseCategories(nodes).Where(x => x.Id == id).FirstOrDefault();

        if (category == null)
        {
            throw new BonusException($"Category {id} not found");
        }

        var childCategories = ParseCategories(nodes.Where(x => x.ParentNode == id).ToList());

        category.SubCategories = childCategories;

        if (!category.SubCategories.Any())
        {
            category.Items = hierarchyResponse.Hierarchy.FirstOrDefault()?
              .Nodes.SelectMany(x => x.Nodes)
              .SelectMany(x => x.Leafs ?? [])
              .Where(x => x.ParentNode == id)
              .Select(x => new ItemData
              {
                  Id = x.Id,
                  Name = x.Description ?? "N/A",
                  Description = x.Description ?? "N/A",
                  ImageUrl = imageUrlService.CreateImageUrl(x.ImageId)
              })
              .ToList() ?? [];
        }

        return category;
    }

    private List<CategoryData> ParseCategories(List<HierarchyNode> nodes)
    {
        var categories = nodes.Select(x =>
             new CategoryData
             {
                 Id = x.Id,
                 Name = x.Description ?? "N/A",
                 ImageUrl = imageUrlService.CreateImageUrl(x.ImageId),
                 ItemCount = x.Leafs?.Count ?? 0,
                 SubcategoryCount = x.Nodes?.Count ?? 0
             })
             .ToList();

        return categories;
    }
}

public class CategoryData
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public int ItemCount { get; set; }

    public int SubcategoryCount { get; set; }

    public List<CategoryData> SubCategories { get; set; } = [];

    public List<ItemData> Items { get; set; } = [];
}

public class ItemData
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string Description { get; set; }

    public required string ImageUrl { get; set; }
}