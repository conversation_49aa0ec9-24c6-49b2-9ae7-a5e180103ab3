using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Catalogue;

public class GetItemsQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetItemsRequest, GetItemsResponse>
{
    public async Task<GetItemsResponse> Handle(GetItemsRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetItemsBySearchRequest
        {
            Search = request.Search ?? "",
            IncludeDetails = false,
        };
        
        var items = await lsRetailAdapter.GetItemsBySearch(apiRequest, cancellationToken);
        
        return new GetItemsResponse
        {
            Items = items.Response!.Items.Select(x => new ItemListData
            {
                Id = x.Id,
                Description = x.Description,
                ImageSource = x.DefaultImage?.Source
            }).ToList()
        };
    }
}

public class GetItemsRequest : IRequest<GetItemsResponse>
{
    public string? Search { get; set; }
}

public class GetItemsResponse
{
    public List<ItemListData> Items { get; set; } = [];
}

public class ItemListData
{
    public required string Id { get; set; }
        
    public required string? Description { get; set; }
    
    public required string? ImageSource { get; set; }
}