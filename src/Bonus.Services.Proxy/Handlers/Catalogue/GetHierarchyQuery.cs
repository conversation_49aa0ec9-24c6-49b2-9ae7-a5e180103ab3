using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Catalogue;

public class GetHierarchyQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetHierarchyRequest, GetHierarchyResponse>
{
    public async Task<GetHierarchyResponse> Handle(GetHierarchyRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetHierarchyRequest()
        {
            StoreId = "01"
        };
        
        var apiResponse = await lsRetailAdapter.GetHierarchy(apiRequest, cancellationToken);
        
        // This is a beauty. (NOOOOOOOOOOOOOOT)
        return new GetHierarchyResponse
        {
            Hierarchies = apiResponse.Response!.Hierarchy.Select(x => new HierarchyData()
            {
                Id = x.Id,
                Description = x.Description,
                Type = x.Type.ToString(),
                Nodes = x.Nodes.Select(x => new HierarchyNode()
                {
                    Id = x.Id,
                    Description = x.Description,
                    ParentNode = x.ParentNode,
                    ImageId = x.ImageId,
                    ChildrenOrder = x.ChildrenOrder,
                    Indentation = x.Indentation,
                    Leafs = x.Leafs.Select(y => new HierarchyLeaf()
                    {
                        Id = y.Id,
                        Description = y.Description,
                        HierarchyCode = y.HierarchyCode,
                        ParentNode = y.ParentNode,
                        ImageId = y.ImageId,
                        SortOrder = y.SortOrder,
                        Type = y.Type.ToString()
                    }).ToList(),
                    Nodes = x.Nodes.Select(y => new HierarchyNode()
                    {
                        Id = y.Id,
                        Description = y.Description,
                        ParentNode = y.ParentNode,
                        ImageId = y.ImageId,
                        ChildrenOrder = y.ChildrenOrder,
                        Indentation = y.Indentation,
                        Leafs = y.Leafs.Select(z => new HierarchyLeaf()
                        {
                            Id = z.Id,
                            Description = z.Description,
                            HierarchyCode = z.HierarchyCode,
                            ParentNode = z.ParentNode,
                            ImageId = z.ImageId,
                            SortOrder = z.SortOrder,
                            Type = z.Type.ToString()
                        }).ToList()
                    }).ToList()
                }).ToList()
            }).ToList()
        };
    }
}

public class GetHierarchyRequest : IRequest<GetHierarchyResponse>;

public class GetHierarchyResponse
{
    public List<HierarchyData> Hierarchies { get; set; } = [];
}

public class HierarchyData
{
    public required string Id { get; set; }
        
    public required string? Description { get; set; }
    
    public required string Type { get; set; }
    
    public required List<HierarchyNode> Nodes { get; set; } = [];
}

public class HierarchyNode
{
    public required string Id { get; set; }
    
    public string? Description { get; set; }
    
    public string? ParentNode { get; set; }
    
    public string? ImageId { get; set; }
    
    public int ChildrenOrder { get; set; }
    
    public int Indentation { get; set; }

    public List<HierarchyLeaf> Leafs { get; set; } = [];
    
    public List<HierarchyNode> Nodes { get; set; } = [];
}

public class HierarchyLeaf
{
    public required string Id { get; set; }
    
    public string? Description { get; set; }

    public string? HierarchyCode { get; set; }
    
    public string? ParentNode { get; set; }
    
    public string? ImageId { get; set; }
    
    public int SortOrder { get; set; }
    
    public required string Type { get; set; }
}