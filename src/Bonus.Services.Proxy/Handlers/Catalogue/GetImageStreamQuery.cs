using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Catalogue;

public class GetImageStreamQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetImageStreamByIdRequest, Stream>
{
    
    public async Task<Stream> Handle(GetImageStreamByIdRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetImageStreamByIdRequest()
        {
            Id = request.ItemId,
            Width = request.Width,
            Height = request.Height
        };
            
        var stream = await lsRetailAdapter.GetImageStreamById(apiRequest, cancellationToken);

        return stream.Response!;
    }
}

public class GetImageStreamByIdRequest : IRequest<Stream>
{
    public required string ItemId { get; set; }
    
    public required int Width { get; set; }
    
    public required int Height { get; set; }
}