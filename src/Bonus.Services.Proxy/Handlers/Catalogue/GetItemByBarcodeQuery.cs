using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Catalogue;

public class GetItemByBarcodeQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetItemByBarcodeRequest, GetItemByBarcodeResponse>
{
    public async Task<GetItemByBarcodeResponse> Handle(GetItemByBarcodeRequest request, CancellationToken cancellationToken)
    {
        // Coca-Cola example value: 5449000000996
        var apiRequest = new LSGetItemByBarcodeRequest
        {
            Barcode = request.Barcode,
            StoreId = request.StoreId ?? ""
        };
            
        var apiRespopnse = await lsRetailAdapter.GetItemByBarcode(apiRequest, cancellationToken);
        
        return new GetItemByBarcodeResponse
        {
            Id = apiRespopnse.Response!.Item.Id,
            Description = apiRespopnse.Response!.Item.Description,
            ImageSource = apiRespopnse.Response!.Item.DefaultImage?.Source
        };
    }
}

public class GetItemByBarcodeRequest : IRequest<GetItemByBarcodeResponse>
{
    public required string Barcode { get; set; }
    
    public string? StoreId { get; set; }
}

public class GetItemByBarcodeResponse
{
    public required string Id { get; set; }
        
    public required string? Description { get; set; }
    
    public required string? ImageSource { get; set; }
}