using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Catalogue;

public class GetItemQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetItemByIdRequest, GetItemByIdResponse>
{
    
    public async Task<GetItemByIdResponse> Handle(GetItemByIdRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetItemGetByIdRequest
        {
            ItemId = request.ItemId,
            StoreId = request.StoreId ?? ""
        };
            
        var item = await lsRetailAdapter.GetItemById(apiRequest, cancellationToken);
        
        return new GetItemByIdResponse
        {
            Id = item.Response!.Item?.Id ?? "",
            Description = item.Response!.Item?.Description ?? "",
            ImageSource = item.Response!.Item?.DefaultImage?.Source
        };
    }
}

public class GetItemByIdRequest : IRequest<GetItemByIdResponse>
{
    public required string ItemId { get; set; }
    
    public string? StoreId { get; set; }
}

public class GetItemByIdResponse
{
    public required string Id { get; set; }
        
    public required string Description { get; set; }
    
    public required string? ImageSource { get; set; }
}