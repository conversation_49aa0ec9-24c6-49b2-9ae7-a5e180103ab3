using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class ShareShoppingListCommand(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<ShareShoppingListRequest, ShareShoppingListResponse>
{
    public async Task<ShareShoppingListResponse> Handle(ShareShoppingListRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSLinkOneListRequest()
        {
            CardId = request.CardId,
            OneListId = request.OneListId,
            Status = LinkStatus.Requesting, // TODO: Use request.Status
        };
            
        var apiResponse = await lsRetailAdapter.LinkOneList(apiRequest, cancellationToken);
        
        return new ShareShoppingListResponse
        {
            Success = apiResponse.Response?.Success ?? false
        };
    }
}

public class ShareShoppingListRequest : IRequest<ShareShoppingListResponse>
{
    public required string CardId { get; set; }
    
    public required string OneListId { get; set; }
    
    // public required string Status { get; set; }
}

public class ShareShoppingListResponse
{
    public required bool Success { get; set; }
}