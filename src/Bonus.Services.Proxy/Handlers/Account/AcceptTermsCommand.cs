using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class AcceptTermsCommand(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<AcceptTermsRequest, AcceptTermsResponse>
{
    public async Task<AcceptTermsResponse> Handle(AcceptTermsRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSAcceptTermsRequest()
        {
            AccountId = request.AccountId,
            DeviceId = request.DeviceId,
            TermsAndConditionsVersion = request.TermsAndConditionsVersion,
            PrivacyPolicyVersion = request.PrivacyPolicyVersion
        };
            
        var apiResponse = await lsRetailAdapter.AcceptTerms(apiRequest, cancellationToken);
        
        return new AcceptTermsResponse
        {
            Success = apiResponse.Response?.Success ?? false,
        };
    }
}

public class AcceptTermsRequest : IRequest<AcceptTermsResponse>
{
    public required string AccountId { get; set; }
    
    public required string DeviceId { get; set; }
    
    public required string TermsAndConditionsVersion { get; set; }
    
    public required string PrivacyPolicyVersion { get; set; }
}

public class AcceptTermsResponse
{
    public required bool Success { get; set; }
}