using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class CreateWalletPassCommand(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<CreateWalletPassRequest, CreateWalletPassResponse>
{
    public async Task<CreateWalletPassResponse> Handle(CreateWalletPassRequest request, CancellationToken cancellationToken)
    {
        // Works only for Apple Wallet.
        var apiRequest = new LSCreateWalletPassRequest()
        {
            CardId = request.CardId,
            PlatformType = PlatformType.iOS,
        };
            
        var apiResponse = await lsRetailAdapter.CreateWalletPass(apiRequest, cancellationToken);
        
        return new CreateWalletPassResponse
        {
            Url = apiResponse.Response!.Url,
        };
    }
}

public class CreateWalletPassRequest : IRequest<CreateWalletPassResponse>
{
    public required string CardId { get; set; }
}

public class CreateWalletPassResponse
{
    public required string Url { get; set; }
}