using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class GetContactsQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetContactsRequest, GetContactsResponse>
{
    public async Task<GetContactsResponse> Handle(GetContactsRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSContactSearchRequest()
        {
            SearchType = ContactSearchType.Name,
            Search = request.Search,
            MaxNumberOfRowsReturned = request.MaxNumberOfRowsReturned ?? 1
        };
            
        var apiResponse = await lsRetailAdapter.ContactSearch(apiRequest, cancellationToken);
        
        return new GetContactsResponse
        {
            Contacts = apiResponse.Response!.Contacts.Select(x => new ContactData()
            {
                Id = x.Id,
                FirstName = x.FirstName,
                LastName = x.LastName,
                Email = x.Email,
                Name = x.Name,
                Account = new()
                {
                    Id = x.Account!.Id,
                    Blocked = x.Account.Blocked,
                    PointBalance = x.Account.PointBalance,
                    Status = x.Account.Status.ToString(),
                    Type = x.Account.Type.ToString()
                },
                Addresses = x.Addresses.Select(x => new AddressData()
                {
                    Address1 = x.Address1,
                    Address2 = x.Address2,
                    City = x.City,
                    PostalCode = x.PostCode,
                    PhoneNumber = x.PhoneNumber,
                    Type = x.Type.ToString()
                }).ToList(),
                OneLists = x.OneLists.Select(x => new OneListData()
                {
                    Id = x.Id,
                    CardId = x.CardId,
                    Description = x.Description,
                    ListType = x.ListType.ToString(),
                    Name = x.Name,
                    TotalAmount = x.TotalAmount,
                    TotalDiscAmount = x.TotalDiscAmount,
                    TotalNetAmount = x.TotalNetAmount,
                    TotalTaxAmount = x.TotalTaxAmount
                }).ToList(),
                Cards = x.Cards.Select(x => new CardData()
                {
                    Id = x.Id,
                    ClubId = x.ClubId,
                    ContractId = x.ContractId,
                    LinkedToAccount = x.LinkedToAccount,
                    Status = x.Status.ToString()
                }).ToList()
            }).ToList()
        };
    }
}

public class GetContactsRequest : IRequest<GetContactsResponse>
{
    public required string Search { get; set; }
    
    public int? MaxNumberOfRowsReturned { get; set; }
}

public class GetContactsResponse
{
    public List<ContactData> Contacts { get; set; } = [];
}

public class ContactData
{
    public required string Id { get; set; }
    
    public string? FirstName { get; set; }
    
    public string? LastName { get; set; }
    
    public string? Email { get; set; }
    
    public string? Name { get; set; }
    
    public required AccountData Account { get; set; }
    
    public List<AddressData> Addresses { get; set; } = [];
    
    public List<OneListData> OneLists { get; set; } = [];
    
    public List<CardData> Cards { get; set; } = [];
}