using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class LoginCommand(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<LoginRequest, LoginResponse>
{
    public async Task<LoginResponse> Handle(LoginRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSSocialLogonRequest()
        {
            Authenticator = request.Authenticator,
            AuthenticationId = request.AuthenticationId,
            DeviceId = request.DeviceId,
            DeviceName = request.DeviceName,
            IncludeDetails = request.IncludeDetails
        };
            
        var apiResponse = await lsRetailAdapter.SocialLogon(apiRequest, cancellationToken);
        
        return new LoginResponse
        {
            Result = new ContactData
            {
                Id = apiResponse.Response!.Contact!.Id,
                Email = apiResponse.Response!.Contact.Email,
                FirstName = apiResponse.Response!.Contact.FirstName,
                LastName = apiResponse.Response!.Contact.LastName,
                Name = apiResponse.Response!.Contact.Name,
                Account = new()
                {
                    Id = apiResponse.Response!.Contact.Account!.Id,
                    Blocked = apiResponse.Response!.Contact.Account.Blocked,
                    PointBalance = apiResponse.Response!.Contact.Account.PointBalance,
                    Status = apiResponse.Response!.Contact.Account.Status.ToString(),
                    Type = apiResponse.Response!.Contact.Account.Type.ToString()
                },
                Addresses = apiResponse.Response!.Contact.Addresses.Select(x => new AddressData()
                {
                    Address1 = x.Address1,
                    Address2 = x.Address2,
                    City = x.City,
                    PostalCode = x.PostCode,
                    PhoneNumber = x.PhoneNumber,
                    Type = x.Type.ToString()
                }).ToList(),
                OneLists = apiResponse.Response!.Contact.OneLists.Select(x => new OneListData()
                {
                    Id = x.Id,
                    CardId = x.CardId,
                    ListType = x.ListType.ToString(),
                    Name = x.Name,
                    Description = x.Description,
                    TotalAmount = x.TotalAmount,
                    TotalDiscAmount = x.TotalDiscAmount,
                    TotalNetAmount = x.TotalNetAmount,
                    TotalTaxAmount = x.TotalTaxAmount
                }).ToList(),
                Cards = apiResponse.Response!.Contact.Cards.Select(x => new CardData()
                {
                    Id = x.Id,
                    ClubId = x.ClubId,
                    ContractId = x.ContractId,
                    LinkedToAccount = x.LinkedToAccount,
                    Status = x.Status.ToString()
                }).ToList()
            }
        };
    }
}

public class LoginRequest : IRequest<LoginResponse>
{
    public required string Authenticator { get; set; }
    
    public required string AuthenticationId { get; set; }
    
    public required string DeviceId { get; set; }
    
    public required string? DeviceName { get; set; }
    
    public required bool IncludeDetails { get; set; }
}

public class LoginResponse
{
    public required ContactData? Result { get; set; }
}