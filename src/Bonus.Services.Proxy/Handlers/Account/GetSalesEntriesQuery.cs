using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class GetSalesEntriesQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetSalesEntriesRequest, GetSalesEntriesResponse>
{
    public async Task<GetSalesEntriesResponse> Handle(GetSalesEntriesRequest request,
        CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetSalesEntriesGetByCardIdRequest
        {
            CardId = request.CardId,
            MaxNumberOfTransactions = request.MaxNumberOfTransactions ?? 100
        };

        var apiResponse = await lsRetailAdapter.GetSalesEntriesByCardId(apiRequest, cancellationToken);

        return new GetSalesEntriesResponse
        {
            SalesEntries = apiResponse.Response!.SalesEntries.Select(x => new SalesEntryData
            {
                Id = x.Id,
                CardId = x.CardId,
                StoreId = x.StoreId,
                StoreName = x.StoreName,
                TerminalId = x.TerminalId,
                StaffId = x.StaffId,
                TransactionNumber = x.TransactionNumber,
                ReceiptNumber = x.ReceiptNumber,
                DocumentRegTime = x.DocumentRegTime,
                DocumentDate = x.DocumentDate.UtcDateTime,
                TotalAmount = x.TotalAmount,
                TotalNetAmount = x.TotalNetAmount,
                TotalDiscount = x.TotalDiscount,
                PointsUsed = x.PointsUsed,
                PointsEarned = x.PointsEarned,
                PointsBalance = x.PointsBalance,
                Lines = x.Lines.Select(l => new SalesEntryLineData
                {
                    Id = l.Id,
                    LineNumber = l.LineNumber,
                    ItemId = l.ItemId,
                    VariantId = l.VariantId,
                    Barcode = l.Barcode,
                    ItemDescription = l.ItemDescription,
                    UnitOfMeasureId = l.UnitOfMeasureId,
                    Quantity = l.Quantity,
                    Price = l.Price,
                    NetPrice = l.NetPrice,
                    DiscountAmount = l.DiscountAmount,
                    NetAmount = l.NetAmount,
                    TaxAmount = l.TaxAmount,
                    Amount = l.Amount,
                    ImageSource = l.Image?.Source
                }).ToList(),
                Discounts = x.Discounts.Select(d => new SalesEntryDiscountData
                {
                    Id = d.Id,
                    Type = d.Type,
                    Number = d.Number,
                    Description = d.Description,
                    Percentage = d.Percentage,
                    Amount = d.Amount
                }).ToList(),
                Payments = x.Payments.Select(p => new SalesEntryPaymentData
                {
                    Id = p.Id,
                    LineNumber = p.LineNumber,
                    TenderTypeId = p.TenderTypeId,
                    CurrencyCode = p.CurrencyCode,
                    Amount = p.Amount,
                    CardOrCustNum = p.CardOrCustNum,
                    CardType = p.CardType
                }).ToList()
            }).ToList()
        };
    }
}

public class GetSalesEntriesRequest : IRequest<GetSalesEntriesResponse>
{
    public required string CardId { get; set; }

    public int? MaxNumberOfTransactions { get; set; }
}

public class GetSalesEntriesResponse
{
    public required List<SalesEntryData> SalesEntries { get; set; } = [];
}

public class SalesEntryData
{
    public required string Id { get; set; }

    public required string CardId { get; set; }

    public required string StoreId { get; set; }

    public required string StoreName { get; set; }

    public required string TerminalId { get; set; }

    public required string StaffId { get; set; }

    public required string TransactionNumber { get; set; }

    public required string ReceiptNumber { get; set; }

    public required string DocumentRegTime { get; set; }

    public required DateTime DocumentDate { get; set; }

    public required decimal TotalAmount { get; set; }

    public required decimal TotalNetAmount { get; set; }

    public required decimal TotalDiscount { get; set; }

    public required decimal PointsUsed { get; set; }

    public required decimal PointsEarned { get; set; }

    public required decimal PointsBalance { get; set; }

    public required List<SalesEntryLineData> Lines { get; set; } = [];

    public required List<SalesEntryDiscountData> Discounts { get; set; } = [];

    public required List<SalesEntryPaymentData> Payments { get; set; } = [];
}

public class SalesEntryLineData
{
    public required string Id { get; set; }

    public required int LineNumber { get; set; }

    public required string ItemId { get; set; }

    public required string VariantId { get; set; }

    public required string Barcode { get; set; }

    public required string ItemDescription { get; set; }

    public required string UnitOfMeasureId { get; set; }

    public required decimal Quantity { get; set; }

    public required decimal Price { get; set; }

    public required decimal NetPrice { get; set; }

    public required decimal DiscountAmount { get; set; }

    public required decimal NetAmount { get; set; }

    public required decimal TaxAmount { get; set; }

    public required decimal Amount { get; set; }

    public string? ImageSource { get; set; }
}

public class SalesEntryDiscountData
{
    public required string Id { get; set; }

    public required string Type { get; set; }

    public required string Number { get; set; }

    public required string Description { get; set; }

    public required decimal Percentage { get; set; }

    public required decimal Amount { get; set; }
}

public class SalesEntryPaymentData
{
    public required string Id { get; set; }

    public required int LineNumber { get; set; }

    public required string TenderTypeId { get; set; }

    public required string CurrencyCode { get; set; }

    public required decimal Amount { get; set; }

    public required string CardOrCustNum { get; set; }

    public required string CardType { get; set; }
}