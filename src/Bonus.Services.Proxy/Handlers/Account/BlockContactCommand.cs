using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class BlockContactCommand(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<BlockContactRequest, BlockContactResponse>
{
    public async Task<BlockContactResponse> Handle(BlockContactRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSContactBlockRequest()
        {
            CardId = request.CardId,
            AccountId = request.AccountId
        };
            
        var response = await lsRetailAdapter.BlockContact(apiRequest, cancellationToken);
        
        return new BlockContactResponse
        {
            Success = response.Response?.Success ?? false
        };
    }
}

public class BlockContactRequest : IRequest<BlockContactResponse>
{
    public required string CardId { get; set; }
    
    public required string AccountId { get; set; }
}

public class BlockContactResponse
{
    public required bool Success { get; set; }
}