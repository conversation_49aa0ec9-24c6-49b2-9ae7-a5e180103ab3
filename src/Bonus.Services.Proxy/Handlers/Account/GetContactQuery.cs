using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class GetContactQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetContactRequest, GetContactResponse>
{
    public async Task<GetContactResponse> Handle(GetContactRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = request.CardId,
            NumberOfTransactionsReturned = 0,
        };
            
        var apiResponse = await lsRetailAdapter.GetContactByCardId(apiRequest, cancellationToken);
        
        return new GetContactResponse
        {
            Id = apiResponse.Response!.Contact!.Id,
            FirstName = apiResponse.Response!.Contact.FirstName,
            LastName = apiResponse.Response!.Contact.LastName,
            Email = apiResponse.Response!.Contact.Email,
            Name = apiResponse.Response!.Contact.Name,
            Account = new()
            {
                Id = apiResponse.Response!.Contact.Account!.Id,
                Blocked = apiResponse.Response!.Contact.Account.Blocked,
                PointBalance = apiResponse.Response!.Contact.Account.PointBalance,
                Status = apiResponse.Response!.Contact.Account.Status.ToString(),
                Type = apiResponse.Response!.Contact.Account.Type.ToString()
            },
            Addresses = apiResponse.Response!.Contact.Addresses.Select(x => new AddressData()
            {
                Address1 = x.Address1,
                Address2 = x.Address2,
                City = x.City,
                PostalCode = x.PostCode,
                PhoneNumber = x.PhoneNumber,
                Type = x.Type.ToString()
            }).ToList(),
            OneLists = apiResponse.Response!.Contact.OneLists.Select(x => new OneListData()
            {
                Id = x.Id,
                CardId = x.CardId,
                ListType = x.ListType.ToString(),
                Name = x.Name,
                Description = x.Description,
                TotalAmount = x.TotalAmount,
                TotalDiscAmount = x.TotalDiscAmount,
                TotalNetAmount = x.TotalNetAmount,
                TotalTaxAmount = x.TotalTaxAmount
            }).ToList(),
            Cards = apiResponse.Response!.Contact.Cards.Select(x => new CardData()
            {
                Id = x.Id,
                ClubId = x.ClubId,
                ContractId = x.ContractId,
                LinkedToAccount = x.LinkedToAccount,
                Status = x.Status.ToString()
            }).ToList()
        };
    }
}

public class GetContactRequest : IRequest<GetContactResponse>
{
    public required string CardId { get; set; }
}

public class GetContactResponse
{
    public required string Id { get; set; }
    
    public string? FirstName { get; set; }
    
    public string? LastName { get; set; }
    
    public string? Email { get; set; }
    
    public string? Name { get; set; }
    
    public required AccountData Account { get; set; }
    
    public List<AddressData> Addresses { get; set; } = [];
    
    public List<OneListData> OneLists { get; set; } = [];
    
    public List<CardData> Cards { get; set; } = [];
}

public class AccountData
{
    public required string Id { get; set; }
    
    public bool Blocked { get; set; }
    
    public int PointBalance { get; set; }
    
    public required string Status { get; set; }
    
    public required string Type { get; set; }
}

public class AddressData
{
    public required string? Address1 { get; set; }
    
    public required string? Address2 { get; set; }
    
    public required string? City { get; set; }

    public required string? PostalCode { get; set; }
    
    public required string? PhoneNumber { get; set; }
    
    public required string Type { get; set; }
}

public class OneListData
{
    public required string Id { get; set; }
    
    public required string CardId { get; set; }
    
    public required string ListType { get; set; }
    
    public string? Name { get; set; }
    
    public required string? Description { get; set; }
    
    public decimal TotalAmount { get; set; }
    
    public decimal TotalDiscAmount { get; set; }
    
    public decimal TotalNetAmount { get; set; }
    
    public decimal TotalTaxAmount { get; set; }
}

public class CardData
{
    public required string Id { get; set; }
    
    public required string ClubId { get; set; }
    
    public required string ContractId { get; set; }
    
    public bool LinkedToAccount { get; set; }
    
    public required string Status { get; set; }
}