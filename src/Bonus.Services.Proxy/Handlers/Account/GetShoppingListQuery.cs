using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class GetShoppingListQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetShoppingListRequest, GetShoppingListResponse>
{
    public async Task<GetShoppingListResponse> Handle(GetShoppingListRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByIdRequest
        {
            Id = request.Id,
            IncludeLines = request.IncludeLines
        };
            
        var apiResponse = await lsRetailAdapter.GetOneListById(apiRequest, cancellationToken);
        
        return new GetShoppingListResponse
        {
            Id = apiResponse.Response!.OneList.Id,
            CardId = apiResponse.Response!.OneList.CardId,
            CardLinks = apiResponse.Response!.OneList.CardLinks.Select(x => new CardLink
            {
                Id = x.Id,
                CardId = x.CardId,
                Name = x.Name,
                Owner = x.Owner,
                Status = x.Status.ToString()
            }).ToList(),
            Name = apiResponse.Response!.OneList.Name,
            Description = apiResponse.Response!.OneList.Description,
            Items = apiResponse.Response!.OneList.Items.Select(x => new ItemData
            {
                Id = x.Id,
                ItemId = x.ItemId,
                ItemDescription = x.ItemDescription,
                Amount = x.Amount,
                Price = x.Price,
                UnitOfMeasureId = x.UnitOfMeasureId,
                Quantity = x.Quantity,
                IsManualItem = x.IsManualItem,
                CreateDate = x.CreateDate.UtcDateTime,
                ImageSource = x.Image?.Source,
            }).ToList()
        };
    }
}

public class GetShoppingListRequest : IRequest<GetShoppingListResponse>
{
    public required string Id { get; set; }
    
    public required bool IncludeLines { get; set; }
}

public class GetShoppingListResponse
{
    public required string Id { get; set; }
    
    public required string CardId { get; set; }

    public required List<CardLink> CardLinks { get; set; } = [];
    
    public required string? Name { get; set; }
    
    public required string? Description { get; set; }
    
    public required List<ItemData> Items { get; set; } = [];
}

public class CardLink
{
    public required string Id { get; set; }
    
    public required string CardId { get; set; }
    
    public required string Name { get; set; }
    
    public required bool Owner { get; set; }
    
    public required string Status { get; set; }
}

public class ItemData
{
    public required string Id { get; set; }
    
    public string? ItemId { get; set; }
    
    public string? ItemDescription { get; set; }
    
    public decimal Amount { get; set; }
    
    public decimal Price { get; set; }
    
    public string? UnitOfMeasureId { get; set; }
    
    public decimal Quantity { get; set; }
    
    public bool IsManualItem { get; set; }
    
    public required DateTime CreateDate { get; set; }
    
    public required string? ImageSource { get; set; }
}