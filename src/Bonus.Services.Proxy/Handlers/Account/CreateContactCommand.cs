using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Members;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class CreateContactCommand(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<CreateContactRequest, CreateContactResponse>
{
    public async Task<CreateContactResponse> Handle(CreateContactRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSContactCreateRequest()
        {
            DoLogin = request.DoLogin,
            Contact = new MemberContact()
            {
                Id = "",
                Account = null,
                Authenticator = request.Contact.Authenticator,
                AuthenticationId = request.Contact.AuthenticationId,
                FirstName = request.Contact.FirstName,
                LastName = request.Contact.LastName,
                Name = request.Contact.Name,
                Email = request.Contact.Email,
                Addresses = request.Contact.Addresses.Select(x => new Address()
                {
                    Address1 = x.Address1,
                    Address2 = x.Address2,
                    City = x.City,
                    PostCode = x.PostalCode,
                    PhoneNumber = x.PhoneNumber,
                    Type = (int) AddressType.Residential // TODO: Map from x.Type enum
                }).ToList(),
                Profiles = request.Contact.Profiles.Select(x => new Profile()
                {
                    Id = x.Id,
                    ContactValue = x.ContactValue,
                    Description = x.Description,
                    TextValue = x.TextValue,
                    Mandatory = x.Mandatory
                }).ToList(),
                Cards = [],
            }
        };
            
        var apiResponse = await lsRetailAdapter.CreateContact(apiRequest, cancellationToken);
        
        return new CreateContactResponse
        {
            Contact = new()
            {
                Id = apiResponse.Response!.Contact.Id,
                FirstName = apiResponse.Response!.Contact.FirstName,
                LastName = apiResponse.Response!.Contact.LastName,
                Name = apiResponse.Response!.Contact.Name,
                Email = apiResponse.Response!.Contact.Email,
                Addresses = apiResponse.Response!.Contact.Addresses.Select(x => new AddressData()
                {
                    Address1 = x.Address1,
                    Address2 = x.Address2,
                    City = x.City,
                    PostalCode = x.PostCode,
                    PhoneNumber = x.PhoneNumber,
                    Type = x.Type.ToString()
                }).ToList(),
                OneLists = [],
                Cards = apiResponse.Response!.Contact.Cards.Select(x => new CardData()
                {
                    Id = x.Id,
                    ClubId = x.ClubId,
                    ContractId = x.ContractId,
                    LinkedToAccount = x.LinkedToAccount,
                    Status = x.Status.ToString()
                }).ToList(),
                Account = new()
                {
                    Id = apiResponse.Response!.Contact.Account!.Id,
                    Blocked = apiResponse.Response!.Contact.Account.Blocked,
                    PointBalance = apiResponse.Response!.Contact.Account.PointBalance,
                    Status = apiResponse.Response!.Contact.Account.Status.ToString(),
                    Type = apiResponse.Response!.Contact.Account.Type.ToString()
                }
            }
        };
    }
}

public class CreateContactRequest : IRequest<CreateContactResponse>
{
    public required CreateContactData Contact { get; set; }
    
    public required bool DoLogin { get; set; }
}

public class CreateContactResponse
{
    public required ContactData Contact { get; set; }
}

public class CreateContactData
{
    public required string Email { get; set; }
    
    public required string Authenticator { get; set; }
    
    public required string AuthenticationId { get; set; }
    
    public string? FirstName { get; set; }
    
    public string? LastName { get; set; }
    
    public string? Name { get; set; }
    
    public List<AddressData> Addresses { get; set; } = [];

    public List<CreateContactProfileData> Profiles { get; set; } = [];
}

public class CreateContactProfileData
{
    public required string Id { get; set; }
    
    public required bool ContactValue { get; set; }
    
    public required string? Description { get; set; }
    
    public required string TextValue { get; set; }
    
    public required bool Mandatory { get; set; }
}