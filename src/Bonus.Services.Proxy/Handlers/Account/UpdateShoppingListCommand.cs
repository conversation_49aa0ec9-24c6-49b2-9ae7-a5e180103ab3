using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Baskets;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class UpdateShoppingListQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<UpdateShoppingListRequest, UpdateShoppingListResponse>
{
    public async Task<UpdateShoppingListResponse> Handle(UpdateShoppingListRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSSaveOneListRequest()
        {
            Calculate = request.Calculate,
            OneList = new()
            {
                CardId = request.ShoppingList.CardId,
                Id = request.ShoppingList.Id,
                CardLinks = request.ShoppingList.CardLinks.Select(x => new OneListLink
                {
                    Id = x.Id,
                    CardId = x.CardId,
                    Name = x.Name,
                    Owner = x.Owner,
                    Status = Enum.Parse<LinkStatus>(x.Status)
                }).ToList(),
                Description = request.ShoppingList.Description,
                Name = request.ShoppingList.Name,
                Items = request.ShoppingList.Items.Select(x => new OneListItem
                {
                    Id = x.Id,
                    ItemId = x.ItemId,
                    ItemDescription = x.ItemDescription,
                    Amount = x.Amount,
                    Price = x.Price,
                    UnitOfMeasureId = x.UnitOfMeasureId,
                    Quantity = x.Quantity,
                    IsManualItem = x.IsManualItem,
                    CreateDate = x.CreateDate,
                })
                .ToList(),
                ListType = ListType.Basket
            }
        };
            
        var apiResponse = await lsRetailAdapter.SaveOneList(apiRequest, cancellationToken);
        
        return new UpdateShoppingListResponse
        {
            Id = apiResponse.Response!.OneList.Id,
            CardId = apiResponse.Response!.OneList.CardId,
            CardLinks = apiResponse.Response!.OneList.CardLinks.Select(x => new CardLink
            {
                Id = x.Id,
                CardId = x.CardId,
                Name = x.Name,
                Owner = x.Owner,
                Status = x.Status.ToString()
            }).ToList(),
            Name = apiResponse.Response!.OneList.Name,
            Description = apiResponse.Response!.OneList.Description,
            Items = apiResponse.Response!.OneList.Items.Select(x => new ItemData
            {
                Id = x.Id,
                ItemId = x.ItemId,
                ItemDescription = x.ItemDescription,
                Amount = x.Amount,
                Price = x.Price,
                UnitOfMeasureId = x.UnitOfMeasureId,
                Quantity = x.Quantity,
                IsManualItem = x.IsManualItem,
                CreateDate = x.CreateDate.UtcDateTime,
                ImageSource = x.Image?.Source,
            }).ToList()
        };
    }
}

public class UpdateShoppingListRequest : IRequest<UpdateShoppingListResponse>
{
    public required bool Calculate { get; set; }
    
    public required ShoppingListData ShoppingList { get; set; }
}

public class UpdateShoppingListResponse
{
    public required string Id { get; set; }
    
    public required string CardId { get; set; }

    public required List<CardLink> CardLinks { get; set; } = [];
    
    public required string? Name { get; set; }

    public required string? Description { get; set; }
    
    public required List<ItemData> Items { get; set; } = [];
}