using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Account;

public class GetShoppingListsQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetShoppingListsRequest, GetShoppingListsResponse>
{
    public async Task<GetShoppingListsResponse> Handle(GetShoppingListsRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetOneListByCardIdRequest
        {
            CardId = request.CardId,
            ListType = ListType.Wish, // TODO: Add support for request.ListType
            IncludeLines = request.IncludeLines
        };
            
        var apiResponse = await lsRetailAdapter.GetOneListByCardId(apiRequest, cancellationToken);
        
        return new GetShoppingListsResponse
        {
            ShoppingLists = apiResponse.Response!.OneLists.Select(x => new ShoppingListData
            {
                Id = x.Id,
                CardId = x.CardId,
                CardLinks = x.CardLinks.Select(y => new CardLink
                {
                    Id = y.Id,
                    CardId = y.CardId,
                    Name = y.Name,
                    Owner = y.Owner,
                    Status = y.Status.ToString()
                }).ToList(),
                Name = x.Name,
                Description = x.Description,
                Items = x.Items.Select(y => new ItemData
                {
                    Id = y.Id,
                    ItemId = y.ItemId,
                    ItemDescription = y.ItemDescription,
                    Amount = y.Amount,
                    Price = y.Price,
                    UnitOfMeasureId = y.UnitOfMeasureId,
                    Quantity = y.Quantity,
                    IsManualItem = y.IsManualItem,
                    CreateDate = y.CreateDate.UtcDateTime,
                    ImageSource = y.Image?.Source,
                }).ToList()
            }).ToList()
        };
    }
}

public class GetShoppingListsRequest : IRequest<GetShoppingListsResponse>
{
    public required string CardId { get; set; }
    
    // public required string ListType { get; set; }
    
    public required bool IncludeLines { get; set; }
}

public class GetShoppingListsResponse
{
    public required List<ShoppingListData> ShoppingLists { get; set; } = [];
}

public class ShoppingListData
{
    public required string Id { get; set; }
    
    public required string CardId { get; set; }

    public required List<CardLink> CardLinks { get; set; } = [];
    
    public required string? Name { get; set; }
    
    public required string? Description { get; set; }
    
    public required List<ItemData> Items { get; set; } = [];
}