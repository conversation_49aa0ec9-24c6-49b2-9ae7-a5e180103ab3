using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Stores;

public class GetStoresByCoordinatesQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetStoresByCoordinatesRequest, GetStoresByCoordinatesResponse>
{
    public async Task<GetStoresByCoordinatesResponse> Handle(GetStoresByCoordinatesRequest request, CancellationToken cancellationToken)
    {
        // We cannot send maxDistance as 0, otherwise we get empty response.
        var apiRequest = new LSGetStoresByCoordinatesRequest
        {
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            MaxDistance = request.MaxDistance ?? 150
        };
        
        var stores = await lsRetailAdapter.GetStoresByCoordinates(apiRequest, cancellationToken);
        
        return new GetStoresByCoordinatesResponse
        {
            Stores = stores.Response!.Stores.Select(x => new GetStoresByCoordinatesListData
            {
                Id = x.Id,
                Address = new GetStoreByCoordinatesResponseAddress
                {
                    Address1 = x.Address.Address1,
                    Address2 = x.Address.Address2,
                    City = x.Address.City,
                    PostalCode = x.Address.PostalCode,
                    Type = x.Address.Type.ToString()
                },
                Description = x.Description,
                Distance = x.Distance,
                Latitude = x.Latitude,
                Longitude = x.Longitude,
                Phone = x.Phone,
                IsClickAndCollect = x.IsClickAndCollect
            }).ToList()
        };
    }
}

public class GetStoresByCoordinatesRequest : IRequest<GetStoresByCoordinatesResponse>
{
    public required decimal Latitude { get; set; }
    
    public required decimal Longitude { get; set; }
    
    public decimal? MaxDistance { get; set; }
}

public class GetStoresByCoordinatesResponse
{
    public List<GetStoresByCoordinatesListData> Stores { get; set; } = [];
}

public class GetStoresByCoordinatesListData
{
    public required string Id { get; set; }
    
    public required GetStoreByCoordinatesResponseAddress Address { get; set; }
        
    public required string? Description { get; set; }
    
    public required decimal Distance { get; set; }
    
    public required decimal Latitude { get; set; }
    
    public required decimal Longitude { get; set; }
    
    public required string? Phone { get; set; }
    
    public required bool IsClickAndCollect { get; set; }
}

public class GetStoreByCoordinatesResponseAddress
{
    public required string? Address1 { get; set; }
    
    public string? Address2 { get; set; } = string.Empty;
    
    public required string? City { get; set; }
    
    public required string? PostalCode { get; set; }
    
    public required string? Type { get; set; }
}