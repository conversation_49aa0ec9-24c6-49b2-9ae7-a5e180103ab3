using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Stores;

public class GetStoreQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetStoreRequest, GetStoreResponse>
{
    public async Task<GetStoreResponse> Handle(GetStoreRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetStoreByIdRequest()
        {
            StoreId = request.StoreId,
            IncludeImages = false
        };
        
        var apiResponse = await lsRetailAdapter.GetStoreById(apiRequest, cancellationToken);
        
        return new GetStoreResponse
        {
            Id = apiResponse.Response!.Store.Id,
            Address = new GetStoreResponseAddress
            {
                Address1 = apiResponse.Response!.Store.Address.Address1,
                Address2 = apiResponse.Response!.Store.Address.Address2,
                City = apiResponse.Response!.Store.Address.City,
                PostalCode = apiResponse.Response!.Store.Address.PostalCode,
                Type = apiResponse.Response!.Store.Address.Type.ToString()
            },
            Description = apiResponse.Response!.Store.Description,
            Distance = apiResponse.Response!.Store.Distance,
            Latitude = apiResponse.Response!.Store.Latitude,
            Longitude = apiResponse.Response!.Store.Longitude,
            Phone = apiResponse.Response!.Store.Phone,
            IsClickAndCollect = apiResponse.Response!.Store.IsClickAndCollect
        };
    }
}

public class GetStoreRequest : IRequest<GetStoreResponse>
{
    public required string StoreId { get; set; }
}

public class GetStoreResponse
{
    public required string Id { get; set; }
    
    public required GetStoreResponseAddress Address { get; set; }
        
    public required string? Description { get; set; }
    
    public required decimal Distance { get; set; }
    
    public required decimal Latitude { get; set; }
    
    public required decimal Longitude { get; set; }
    
    public required string? Phone { get; set; }
    
    public required bool IsClickAndCollect { get; set; }
}