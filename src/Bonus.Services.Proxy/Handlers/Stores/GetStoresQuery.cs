using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Stores;

public class GetStoresQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetStoresRequest, GetStoresResponse>
{
    public async Task<GetStoresResponse> Handle(GetStoresRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetStoresRequest
        {
            StoreGetType = StoreGetType.All,
            IncludeDetails = false,
            IncludeImages = false
        };
        
        var stores = await lsRetailAdapter.GetStores(apiRequest, cancellationToken);
        
        return new GetStoresResponse
        {
            Stores = stores.Response!.Stores.Select(x => new GetStoresListData
            {
                Id = x.Id,
                Address = new GetStoreResponseAddress
                {
                    Address1 = x.Address.Address1,
                    Address2 = x.Address.Address2,
                    City = x.Address.City,
                    PostalCode = x.Address.PostalCode,
                    Type = x.Address.Type.ToString()
                },
                Description = x.Description,
                Distance = x.Distance,
                Latitude = x.Latitude,
                Longitude = x.Longitude,
                Phone = x.Phone,
                IsClickAndCollect = x.IsClickAndCollect
            }).ToList()
        };
    }
}

public class GetStoresRequest : IRequest<GetStoresResponse>;

public class GetStoresResponse
{
    public List<GetStoresListData> Stores { get; set; } = [];
}

public class GetStoresListData
{
    public required string Id { get; set; }
    
    public required GetStoreResponseAddress Address { get; set; }
        
    public required string? Description { get; set; }
    
    public required decimal Distance { get; set; }
    
    public required decimal Latitude { get; set; }
    
    public required decimal Longitude { get; set; }
    
    public required string? Phone { get; set; }
    
    public required bool IsClickAndCollect { get; set; }
}

public class GetStoreResponseAddress
{
    public required string? Address1 { get; set; }
    
    public string? Address2 { get; set; } = string.Empty;
    
    public required string? City { get; set; }
    
    public required string? PostalCode { get; set; }
    
    public required string? Type { get; set; }
}