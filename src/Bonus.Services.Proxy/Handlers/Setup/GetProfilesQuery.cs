using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Setup;

public class GetProfilesQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetProfilesRequest, GetProfilesResponse>
{
    public async Task<GetProfilesResponse> Handle(GetProfilesRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSGetAllProfilesRequest();
        
        var apiResponse = await lsRetailAdapter.GetAllProfiles(apiRequest, cancellationToken);
        
        return new GetProfilesResponse
        {
            Profiles = apiResponse.Response!.Profiles.Select(x => new ProfileData()
            {
                Id = x.Id,
                ContactValue = x.ContactValue,
                Description = x.Description,
                Mandatory = x.Mandatory
            }).ToList()
        };
    }
}

public class GetProfilesRequest : IRequest<GetProfilesResponse>;

public class GetProfilesResponse
{
    public List<ProfileData> Profiles { get; set; } = [];
}

public class ProfileData
{
    public required string Id { get; set; }
    
    public required bool ContactValue { get; set; }
    
    public required string? Description { get; set; }
    
    public required bool Mandatory { get; set; }
}