using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Setup;

public class GetEnvironmentQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetEnvironmentRequest, GetEnvironmentResponse>
{
    public async Task<GetEnvironmentResponse> Handle(GetEnvironmentRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSEnvironmentRequest();
        
        var apiResponse = await lsRetailAdapter.Environment(apiRequest, cancellationToken);
        
        return new GetEnvironmentResponse
        {
            PasswordPolicy = apiResponse.Response!.Environment.PasswordPolicy,
            Version = apiResponse.Response!.Environment.Version
        };
    }
}

public class GetEnvironmentRequest : IRequest<GetEnvironmentResponse>;

public class GetEnvironmentResponse
{
    public required string? PasswordPolicy { get; set; }
    
    public required string? Version { get; set; }
}