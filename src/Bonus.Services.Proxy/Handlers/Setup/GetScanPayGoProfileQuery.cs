using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using MediatR;

namespace Bonus.Services.Proxy.Handlers.Setup;

public class GetScanPayGoProfileQuery(ILSRetailAdapter lsRetailAdapter)
    : IRequestHandler<GetScanPayGoProfileRequest, GetScanPayGoProfileResponse>
{
    public async Task<GetScanPayGoProfileResponse> Handle(GetScanPayGoProfileRequest request, CancellationToken cancellationToken)
    {
        var apiRequest = new LSScanPayGoProfileRequest()
        {
            ProfileId = "",
            StoreId = "",
        };
        
        var apiResponse = await lsRetailAdapter.GetScanPayGoProfile(apiRequest, cancellationToken);
        
        return new GetScanPayGoProfileResponse
        {
            Id = apiResponse.Response!.ScanPayGoProfile.Id,
            Flags = apiResponse.Response!.ScanPayGoProfile.Flags.Flags.Select(x => new FlagData()
            {
                Name = x.Name.ToString(),
                Value = x.Value
            }).ToList()
        };
    }
}

public class GetScanPayGoProfileRequest : IRequest<GetScanPayGoProfileResponse>;

public class GetScanPayGoProfileResponse
{
    public required string Id { get; set; }
    
    public required List<FlagData> Flags { get; set; } = [];
}

public class FlagData
{
    public required string Name { get; set; }
    
    public required string? Value { get; set; }
}