using Bonus.Api.Constants;
using Bonus.Services.Proxy.Handlers.Account;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers.Proxy;

[ApiController]
[Route("proxy/account")]
[ApiExplorerSettings(GroupName = ApiVersions.Proxy)]
public class AccountController(IMediator mediator)
{
    [AllowAnonymous]
    [HttpGet("contacts")]
    public async Task<GetContactsResponse> GetContacts([FromQuery] GetContactsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("contact")]
    public async Task<GetContactResponse> GetContact([FromQuery] GetContactRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    /// <summary>
    /// TODO: Check why this doesn't work on LS side currently.
    /// </summary>
    [AllowAnonymous]
    [HttpPost("block")]
    public async Task<BlockContactResponse> BlockContact([FromBody] BlockContactRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpPost("create-wallet-pass")]
    public async Task<CreateWalletPassResponse> CreateWalletPass([FromBody] CreateWalletPassRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("create")]
    public async Task<CreateContactResponse> CreateContact([FromQuery] CreateContactRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpPost("accept-terms")]
    public async Task<AcceptTermsResponse> AcceptTerms([FromBody] AcceptTermsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("shopping-lists")]
    public async Task<GetShoppingListsResponse> GetShoppingLists([FromQuery] GetShoppingListsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("shopping-list/{id}")]
    public async Task<GetShoppingListResponse> GetShoppingList([FromRoute] string id, CancellationToken cancellationToken)
    {
        var request = new GetShoppingListRequest
        {
            Id = id,
            IncludeLines = true,
        };
        
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("sales-entries")]
    public async Task<GetSalesEntriesResponse> GetSalesEntries([FromQuery] GetSalesEntriesRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpPost("shopping-list")]
    public async Task<UpdateShoppingListResponse> UpdateShoppingList([FromBody] UpdateShoppingListRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpPost("shopping-list-share")]
    public async Task<ShareShoppingListResponse> ShareShoppingList([FromBody] ShareShoppingListRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
}