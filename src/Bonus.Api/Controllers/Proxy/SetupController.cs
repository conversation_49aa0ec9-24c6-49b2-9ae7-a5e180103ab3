using Bonus.Api.Constants;
using Bonus.Services.Proxy.Handlers.Setup;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers.Proxy;

[ApiController]
[Route("proxy/setup")]
[ApiExplorerSettings(GroupName = ApiVersions.Proxy)]
public class SetupController(IMediator mediator)
{
    [AllowAnonymous]
    [HttpGet("profiles")]
    public async Task<GetProfilesResponse> GetProfiles(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetProfilesRequest(), cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("environment")]
    public async Task<GetEnvironmentResponse> GetEnvironment(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetEnvironmentRequest(), cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("scanpaygo")]
    public async Task<GetScanPayGoProfileResponse> GetScanPayGoProfile(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetScanPayGoProfileRequest(), cancellationToken);
    }
}