using Bonus.Api.Constants;
using Bonus.Services.Proxy.Handlers.Catalogue;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers.Proxy;

[ApiController]
[Route("proxy/catalogue")]
[ApiExplorerSettings(GroupName = ApiVersions.Proxy)]
public class CatalogueController(IMediator mediator)
{
    [AllowAnonymous]
    [HttpGet("items")]
    public async Task<GetItemsResponse> GetItems([FromQuery] GetItemsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("items/{itemId}")]
    public async Task<GetItemByIdResponse> GetItemById([FromRoute] string itemId, [FromQuery] string? storeId, CancellationToken cancellationToken)
    {
        var request = new GetItemByIdRequest
        {
            ItemId = itemId,
            StoreId = storeId
        };

        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("items/barcode/{barcode}")]
    public async Task<GetItemByBarcodeResponse> GetItemByBarcode([FromRoute] string barcode, [FromQuery] string? storeId, CancellationToken cancellationToken)
    {
        var request = new GetItemByBarcodeRequest
        {
            Barcode = barcode,
            StoreId = storeId
        };

        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("hierarchy")]
    public async Task<GetHierarchyResponse> GetHierarchy(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetHierarchyRequest(), cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("image/{itemId}")]
    public async Task<Stream> GetImage([FromRoute] string itemId, [FromQuery] int width, [FromQuery] int height, CancellationToken cancellationToken)
    {
        var request = new GetImageStreamByIdRequest()
        {
            ItemId = itemId,
            Width = width,
            Height = height
        };

        return await mediator.Send(request, cancellationToken);
    }
}