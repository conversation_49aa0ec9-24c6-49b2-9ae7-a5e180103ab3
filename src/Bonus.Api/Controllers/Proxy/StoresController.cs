using Bonus.Api.Constants;
using Bonus.Services.Proxy.Handlers.Stores;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers.Proxy;

[ApiController]
[Route("proxy/stores")]
[ApiExplorerSettings(GroupName = ApiVersions.Proxy)]
public class StoresController(IMediator mediator)
{
    [AllowAnonymous]
    [HttpGet("all")]
    public async Task<GetStoresResponse> GetStores(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetStoresRequest(), cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("id/{storeId}")]
    public async Task<GetStoreResponse> GetStores([FromRoute] string storeId, CancellationToken cancellationToken)
    {
        var request = new GetStoreRequest()
        {
            StoreId = storeId,
        };
        
        return await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpGet("coordinates")]
    public async Task<GetStoresByCoordinatesResponse> GetStoresByCoordinates([FromQuery] GetStoresByCoordinatesRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
}