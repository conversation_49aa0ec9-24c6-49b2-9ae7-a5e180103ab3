using Bonus.Api.Constants;
using Bonus.Services.Handlers.Stores;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[AllowAnonymous]
[ApiController]
[Route("api/stores")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class StoresController(IMediator mediator)
{
    [HttpGet]
    public async Task<GetStoresResponse> GetStores([FromQuery] GetStoresRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
}