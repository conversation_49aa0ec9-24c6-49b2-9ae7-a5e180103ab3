using System.Globalization;
using System.Text;
using Bonus.Api.Constants;
using Bonus.Api.Handlers;
using Bonus.Api.Helpers;
using Bonus.Api.Providers;
using Bonus.Api.Resolvers;
using Bonus.Shared.Configuration.Settings;
using Bonus.Shared.Types.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.IdentityModel.Tokens;

namespace Bonus.Api.Extensions;

public static class StartupExtensions
{
    public static void AddBonusProblemDetails(this IServiceCollection services)
    {
        services.AddTransient<IProblemDetailsWriter, BonusProblemDetailsWriter>();

        services.AddExceptionHandler<GlobalExceptionHandler>();

        services.AddProblemDetails(options =>
        {
            options.CustomizeProblemDetails = context =>
            {
                var activity = context.HttpContext.Features.Get<IHttpActivityFeature>()?.Activity;

                context.ProblemDetails.Instance = $"{context.HttpContext.Request.Method} {context.HttpContext.Request.Path}";
                context.ProblemDetails.Extensions.TryAdd("requestId", context.HttpContext.TraceIdentifier);
                context.ProblemDetails.Extensions.TryAdd("traceId", activity?.Id);
            };
        });
    }

    public static void AddLocalizationConfiguration(this IServiceCollection services)
    {
        services.AddLocalization();

        var supportedCultures = new[]
        {
            new CultureInfo("is-IS"),
            new CultureInfo("en-GB"),
        };

        var defaultCulture = supportedCultures[0];

        services.Configure<RequestLocalizationOptions>(options =>
        {
            options.SupportedCultures = supportedCultures;
            options.SupportedUICultures = supportedCultures;
            options.RequestCultureProviders.Clear();
            options.RequestCultureProviders.Add(new UserRequestCultureProvider(defaultCulture));
        });
    }

    public static WebApplication UseBonusSwagger(this WebApplication app)
    {
        // Currently will be enabled on all deployments.
        app.MapOpenApi();
        app.UseSwaggerUI(options =>
        {
            options.DisplayRequestDuration();
            options.SwaggerEndpoint($"/openapi/{ApiVersions.Current}.json", "Bonus Api");
            options.SwaggerEndpoint($"/openapi/{ApiVersions.Proxy}.json", "LS Retail Proxy Api");
        });

        return app;
    }

    public static void AddSwagger(this IServiceCollection services)
    {
        // Proxy LS Retail API - v0.1
        services.AddOpenApi(ApiVersions.Proxy, options =>
        {
            options.AddDocumentTransformer<BearerSecuritySchemeTransformer>();

            // https://github.com/dotnet/aspnetcore/issues/57332#issuecomment-**********
            options.AddDocumentTransformer((document, _, _) =>
            {
                document.Servers = [];
                return Task.CompletedTask;
            });
        });

        // Bonus API - v1
        services.AddOpenApi(ApiVersions.Current, options =>
        {
            options.AddDocumentTransformer<BearerSecuritySchemeTransformer>();

            // https://github.com/dotnet/aspnetcore/issues/57332#issuecomment-**********
            options.AddDocumentTransformer((document, _, _) =>
            {
                document.Servers = [];
                return Task.CompletedTask;
            });
        });
    }

    public static void AddTokenAuthConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        var jwtSection = configuration.GetSection(nameof(JwtSettings));
        services.Configure<JwtSettings>(jwtSection);

        var jwtSettings = jwtSection.Get<JwtSettings>();

        services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(x =>
            {
                x.SaveToken = true;
                x.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = jwtSettings!.Issuer,
                    ValidAudience = jwtSettings.Audience,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.Secret)),
                    ClockSkew = TimeSpan.FromSeconds(3),
                    RequireExpirationTime = true
                };
            });
    }

    public static void AddBonusClaimsResolver(this IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.AddTransient<IBonusUser, UserClaimsResolver>();
    }
}