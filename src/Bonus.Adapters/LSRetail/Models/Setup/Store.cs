namespace Bonus.Adapters.LSRetail.Models.Setup;

public class Store
{
    public required string Id { get; set; }
    
    public required Address Address { get; set; }
        
    public string? Description { get; set; }
    
    public decimal Distance { get; set; }
    
    public decimal Latitude { get; set; }
    
    public decimal Longitude { get; set; }
    
    public string? Phone { get; set; }
    
    public bool IsClickAndCollect { get; set; }
    
    public List<StoreHours> StoreHours { get; set; } = [];
}