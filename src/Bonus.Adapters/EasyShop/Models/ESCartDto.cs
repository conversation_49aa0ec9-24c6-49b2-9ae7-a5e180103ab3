using System.Text.Json.Serialization;

namespace Bonus.Adapters.EasyShop.Models;

public class ESCartDto
{
    [JsonPropertyName("id")]
    public required string Id { get; set; }
    
    [JsonPropertyName("paymentReference")]
    public string? PaymentReference { get; set; }
    
    [JsonPropertyName("paymentAllowed")]
    public required bool PaymentAllowed { get; set; }
    
    [JsonPropertyName("total")]
    public required decimal Total { get; set; }
    
    [JsonPropertyName("totalDiscount")]
    public required decimal TotalDiscount { get; set; }
    
    [JsonPropertyName("items")]
    public required List<ESCartItemDto> Items { get; set; } = [];
    
    [JsonPropertyName("vats")]
    public required List<ESVatDto> VATs { get; set; } = [];
}