using System.Text.Json.Serialization;
using Bonus.Adapters.EasyShop.Enums;

namespace Bonus.Adapters.EasyShop.Models;

public class ESPutEndShoppingTripResponseDto
{
    [JsonPropertyName("id")]
    public required string Id { get; set; }
    
    [JsonPropertyName("endShoppingTripBarcode")]
    public required string EndShoppingTripBarcode { get; set; }
   
    [JsonPropertyName("paymentReference")]
    public string? PaymentReference { get; set; }
    
    [JsonPropertyName("shopper")]
    public required ESShopperDto Shopper { get; set; }
    
    [JsonPropertyName("cart")]
    public required ESCartDto Cart { get; set; }
    
    [JsonPropertyName("paymentAllowedStatus")]
    public required PaymentAllowedStatus PaymentAllowedStatus { get; set; }
    
    [JsonPropertyName("paymentBlockedReason")]
    public PaymentBlockedReason? PaymentBlockedReason { get; set; }
    
    [JsonPropertyName("shoppingTripStatus")]
    public required ShoppingTripStatus ShoppingTripStatus { get; set; }
}