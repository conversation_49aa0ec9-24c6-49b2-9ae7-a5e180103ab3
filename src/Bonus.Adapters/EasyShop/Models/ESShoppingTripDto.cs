using System.Text.Json.Serialization;

namespace Bonus.Adapters.EasyShop.Models;

public class ESShoppingTripDto
{
    [JsonPropertyName("id")]
    public required string Id { get; set; }
    
    [JsonPropertyName("shopper")]
    public required ESShopperDto Shopper { get; set; }
    
    [JsonPropertyName("cart")]
    public required ESCartDto Cart { get; set; }
    
    [JsonPropertyName("endShoppingTripBarcode")]
    public required string EndShoppingTripBarcode { get; set; }
}