using System.Text.Json.Serialization;
using Bonus.Adapters.EasyShop.Enums;

namespace Bonus.Adapters.EasyShop.Models;

public class ESEndShoppingTripStateDto
{
    [JsonPropertyName("id")]
    public string? Id { get; set; }
    
    [JsonPropertyName("paymentAllowedStatus")]
    public PaymentAllowedStatus PaymentAllowedStatus { get; set; }
    
    [JsonPropertyName("paymentBlockedReason")]
    public PaymentBlockedReason? PaymentBlockedReason { get; set; }
    
    [JsonPropertyName("paymentReference")]
    public string? PaymentReference { get; set; }
    
    [JsonPropertyName("shoppingTripStatus")]
    public ShoppingTripStatus ShoppingTripStatus { get; set; }
    
    [JsonPropertyName("cart")]
    public required ESCartDto Cart { get; set; }
}