using System.Net;
using Bonus.Adapters.EasyShop.Models;
using Bonus.Adapters.EasyShop.Refit;
using Bonus.Shared.Types.Common;
using Bonus.Shared.Types.Common.Exceptions;
using Microsoft.Extensions.Logging;
using Refit;

namespace Bonus.Adapters.EasyShop;

public class EasyShopAdapter(IEasyShopApi easyShopApi, ILogger<EasyShopAdapter> logger) : IEasyShopAdapter
{
    public async Task<Result<ESShoppingTripDto, ESErrorDto>> GetShoppingTrip(EasyShopGetShoppingTripRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.GetShoppingTrip(request.StoreId, request.CartId, request.ShopperIdentifier, request.AcceptLanguage, request.DeviceId, request.CorrelationId, ct), cancellationToken);

    public async Task<Result<ESCartDto, ESErrorDto>> GetFinalCart(EasyShopGetFinalCartRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.GetFinalCart(request.StoreId, request.CartId!, request.ShopperIdentifier, request.AcceptLanguage, request.DeviceId, request.CorrelationId, ct), cancellationToken);

    public async Task<Result<ESEndShoppingTripStateDto, ESErrorDto>> GetEndShoppingTripState(EasyShopGetEndShoppingTripStateRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.GetEndShoppingTripState(request.StoreId, request.CartId!, request.ShopperIdentifier, request.AcceptLanguage, request.DeviceId, request.CorrelationId, ct), cancellationToken);

    public async Task<Result<ESShoppingTripDto, ESErrorDto>> BeginShoppingTrip(EasyShopBeginShoppingTripRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.BeginShoppingTrip(request.StoreId, request.Body, request.ShopperIdentifier, request.DeviceId, request.CorrelationId, ct), cancellationToken);

    public async Task<Result<ESCartDto, ESErrorDto>> AddRemoveItem(EasyShopAddRemoveItemRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.AddRemoveItem(request.StoreId, request.CartId!, request.Body, request.ShopperIdentifier, request.AcceptLanguage, request.DeviceId, request.CorrelationId, ct), cancellationToken);

    public async Task<Result<ESCartDto, ESErrorDto>> ChangeTripItemQuantity(EasyShopChangeTripItemQuantityRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.ChangeTripItemQuantity(request.StoreId, request.CartId!, request.ItemId, request.Body, request.ShopperIdentifier, request.AcceptLanguage, request.DeviceId, request.CorrelationId, ct), cancellationToken);

    public async Task<Result<ESCartDto, ESErrorDto>> RemoveTripItem(EasyShopRemoveTripItemRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.RemoveTripItem(request.StoreId, request.CartId!, request.ItemId, request.ShopperIdentifier, request.AcceptLanguage, request.DeviceId, request.CorrelationId, ct), cancellationToken);

    public async Task<Result<ESPutEndShoppingTripResponseDto, ESErrorDto>> EndShoppingTrip(EasyShopEndShoppingTripRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.EndShoppingTrip(request.StoreId, request.CartId!, request.Body, request.ShopperIdentifier, request.AcceptLanguage, request.DeviceId, request.CorrelationId, ct), cancellationToken);

    public async Task<Result<ESCancelShoppingTripResponseDto, ESErrorDto>> CancelShoppingTrip(EasyShopCancelShoppingTripRequest request, CancellationToken cancellationToken) => 
        await CallAsync(ct => easyShopApi.CancelShoppingTrip(request.StoreId, request.CartId!, request.ShopperIdentifier, request.AcceptLanguage, request.DeviceId, request.CorrelationId, ct), cancellationToken);
    
    private async Task<Result<TResponse, ESErrorDto>> CallAsync<TResponse>(Func<CancellationToken, Task<ApiResponse<TResponse>>> apiCall, CancellationToken cancellationToken) where TResponse : class
    {
        var response = await apiCall(cancellationToken);

        if (response.StatusCode is HttpStatusCode.InternalServerError)
        {
            throw new BonusException("Unexpected error occurred while processing the request.");
        }
        
        if (response.IsSuccessStatusCode && response.Error is null)
        {
            return Result<TResponse, ESErrorDto>.Ok(response.Content!);
        }
        
        var rawContent = response.Error.Content;
        
        logger.LogWarning("Received error response from EasyShop API: {StatusCode} - {Content}",
            response.StatusCode, rawContent ?? "No content");

        var errorResponse = await response.Error.GetContentAsAsync<ESErrorDto>();

        if (errorResponse is null)
        {
            throw new BonusException("Failed to parse error response from EasyShop API.");
        }
        
        return Result<TResponse, ESErrorDto>.BadRequest(errorResponse, rawContent!);
    }
}

public interface IEasyShopAdapter
{
    Task<Result<ESShoppingTripDto, ESErrorDto>> GetShoppingTrip(EasyShopGetShoppingTripRequest request, CancellationToken cancellationToken);
    
    Task<Result<ESCartDto, ESErrorDto>> GetFinalCart(EasyShopGetFinalCartRequest request, CancellationToken cancellationToken);
    
    Task<Result<ESEndShoppingTripStateDto, ESErrorDto>> GetEndShoppingTripState(EasyShopGetEndShoppingTripStateRequest request, CancellationToken cancellationToken);
    
    Task<Result<ESShoppingTripDto, ESErrorDto>> BeginShoppingTrip(EasyShopBeginShoppingTripRequest request, CancellationToken cancellationToken);
    
    // Cart item operations
    Task<Result<ESCartDto, ESErrorDto>> AddRemoveItem(EasyShopAddRemoveItemRequest request, CancellationToken cancellationToken);
    
    Task<Result<ESCartDto, ESErrorDto>> ChangeTripItemQuantity(EasyShopChangeTripItemQuantityRequest request, CancellationToken cancellationToken);
    
    Task<Result<ESCartDto, ESErrorDto>> RemoveTripItem(EasyShopRemoveTripItemRequest request, CancellationToken cancellationToken);
    
    Task<Result<ESPutEndShoppingTripResponseDto, ESErrorDto>> EndShoppingTrip(EasyShopEndShoppingTripRequest request, CancellationToken cancellationToken);
    
    Task<Result<ESCancelShoppingTripResponseDto, ESErrorDto>> CancelShoppingTrip(EasyShopCancelShoppingTripRequest request, CancellationToken cancellationToken);
}