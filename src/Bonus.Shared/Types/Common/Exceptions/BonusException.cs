using System.Resources;
using Bonus.Resources;

namespace Bonus.Shared.Types.Common.Exceptions;

public class BonusException : Exception
{
    private static readonly ResourceManager ResourceManager = new(typeof(Translations));
    
    public BonusException(string keyOrMessage) : base(keyOrMessage)
    {
        SetLocalizedMessage(keyOrMessage);
    }
    
    public BonusException(string keyOrMessage, string responseIdentifier) : base(keyOrMessage)
    {
        ResponseIdentifier = responseIdentifier;
    }

    public BonusException(string keyOrMessage, Exception exception) : base(keyOrMessage, exception)
    {
        SetLocalizedMessage(keyOrMessage);
    }
    
    public BonusException(string keyOrMessage, string responseIdentifier, params object[] args) : base(keyOrMessage)
    {
        ResponseIdentifier = responseIdentifier;
        Args = args;
    }
    
    public string? ResponseIdentifier { get; }
    
    public string LocalizedMessage { get; set; } = null!;

    private object[]? Args { get; }
    
    private void SetLocalizedMessage(string key)
    {
        var localizedMessage = ResourceManager.GetString(key) ?? key;
        LocalizedMessage = Args?.Length > 0 ? string.Format(localizedMessage!, Args) : localizedMessage;
    }
}