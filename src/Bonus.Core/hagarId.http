### start auth
POST https://func-hagarmiddlelayer-auth-dev.azurewebsites.net/api/StartAuthHttp?code=4ErySHrjNhLQwlWJfbINGWNBHhevp25Bdpl2HDAX9Q2bAzFuNNr8rw==
Content-Type: application/json

{
  "NationalId": "3005752939",
  "Company": "HagkaupWine",
  "Message": "Olla from hagkaup"
}

### get auth status
POST https://func-hagarmiddlelayer-auth-dev.azurewebsites.net/api/CheckForAuthResponse?code=Et2O9-WQjt7qBrFcP3lxcArQWHUtmOVm0oQ8zCar6VAXAzFuWBE0qg==
Content-Type: application/json

{
  "SessionId": "RqPeuax7hm+RqbB8eIdwvHacB505hcnpBuhOIL2bp7FpsSBfMHZMY0gMDldZH310eomsAkzgtEeTjrKeh6pUvQ=="
}